- ## C#实现ROI编辑工具

  - ## 目 录
  
  - 一、
  
  - 一、功能需求分析
  
    - **1、ROI的基本绘制功能。**
      - 矩形：平移、调整大小。

      - 旋转矩形：平移、旋转、调整大小。

      - 圆形：平移、调整大小。ROI

      - 多边形：平移、调整大小，支持单独调整单个点位置。
  
      - 高级平移：全选所有ROI移动功能。
    - **2、	ROI的其他绘制状态**
    - 选中状态：选中状态显示半透明填充颜色。
    - 感兴趣状态：ROI设置为感兴趣状态时，始终显示红色。
  
    - **3、ROI跟随图像移动功能。**
      - 图像平移：ROI跟随平移，在图像上的位置不变。
      - 图像缩放：ROI跟随缩放，在图像上的位置不变。
  
    - **4、操作的撤销、重做功能。**
      - 撤销：返回上一步操作。
      - 重做：撤销后恢复撤销前的操作。
  
    - **5、ROI复制、粘贴功能。**
      - 复制：复制绘制的ROI、变更其名称ID。
      - 粘贴：粘贴复制的ROI、初始位置为当前鼠标位置。
      - 高级复制粘贴：多选或全选复制粘贴ROI功能。
  
    - **6、ROI区域截取功能。**
      - 截取：获取与图像交集部分图像。
      - 



二、参考图片

<img src="C:\Users\<USER>\Desktop\参考图片.png " alt="参考图片" style="zoom:80%;" />

三、说明2	




四、说明2	



五、说明2	