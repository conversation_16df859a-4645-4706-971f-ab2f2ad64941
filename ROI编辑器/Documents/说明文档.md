- ## ROI编辑器说明文档

  - #### 前言：

    - 先把自己的想法写出来在整理思路吧，不然等下忘记了。
    - 这里说下个人理解吧，这个项目案例是想实现ROI的管理功能。在自定义窗体控件中实现图像显示和各种ROI的绘制。这是基本功能。
    - ROI类型包括：矩形、旋转矩形、圆形、椭圆形，额外有线形、自定义等。
    - 在这之后我又想到了做更多的功能，如撤销、重做这些功能，创建的模版保存加载，总之是参考海康VM的模版编辑器。

  - #### 结构说明

    - **UserControls：**
    - 讲完这些要讲一下结构了。这个案例通过在图像控件中显示图像、绘制ROI。那么首先要有ROI才行，所以我们创建一个UsreControls 目录存放图像控件。我将它命名为UImageWindow。大概是用户图像控件的意思吧！在里面实现图像的显示，及ROI的绘制管理，具体细节后面补充。
    - **ShapeManager：**
    - 要绘制不同的ROI肯定要先定义它。那么是创建不同的类怎么实现统一？或者说创建不同的类应该如何管理？我们可以创建一个ShapeManager 目录存放这些ROI类。然后创建一个Shape类和一个DrawObject类。在Shape类中定义声明ROI的基本字段、属性，在DrawObject创建绘制相关的方法，并用DrawObject继承Shape类，做到分离效果。
    - 在DrawObject类中创建ROI的绘制方法。其他ROI只需要继承DrawObject重写需要的方法即可，方便后期管理。
    - 创建ShapeList类：ROI列表类，用于管理所有的ROI，如ROI的增删改查、状态变更、以及序列化加载保存等。
    - 创建自定义ROI,如矩形ROI，命名：**URectangle**,该类继承DrawObject。并重写自己需要的父类方法，在其中实现自己的绘制逻辑。
    - **Commands**
    - 上面的内容已经实现了基础的ROI绘制功能，但是我们要效果并不止如此，我们还需要创建对应的撤销重做命令。可以创建不同的命令实现撤销和重做功能。首先我们创建一个ICommand 接口声明Undo 和Redo方法，传入ShapeList对象作为参数，实现对不同形状的撤销重做管理。再创建其他命令实现该接口，并重写Undo 、Redo方法以及其他辅助方法。
    - 比如创建CommandAdd类实现ICommand接口及其方。创建构造函数时传入一个DrawObject 对象，在需要撤销重做时调用Undo或Redo方法通过使用ShapeList对象管理DrawObject对象即可(如调用ShapeList的删除和添加)。
    - CommandDelete 删除选中ROI、CommandDeleteAll删除所有ROI、CommandChangeState(ROI)状态变更，UndoManager撤销管理（命令管理）。


结构说明：



结构说明：



结构说明：



结构说明：



结构说明：



结构说明：



结构说明：















