import matplotlib.pyplot as plt
import pandas as pd
import time
import os

def plot_training_progress(results_path):
    plt.ion()  # 开启交互模式
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    while True:
        try:
            # 读取损失文件
            loss_file = os.path.join(results_path, 'losses.csv')
            if os.path.exists(loss_file):
                df = pd.read_csv(loss_file)
                
                # 绘制损失曲线
                ax1.clear()
                ax1.plot(df['epoch'], df['loss'], 'r-', label='Total Loss')
                ax1.plot(df['epoch'], df['loss_seg'], 'b-', label='Seg Loss')
                ax1.plot(df['epoch'], df['loss_dec'], 'g-', label='Dec Loss')
                ax1.set_xlabel('Epoch')
                ax1.set_ylabel('Loss')
                ax1.legend()
                ax1.grid(True)
                ax1.set_title('Training Loss')
                
                # 如果有验证数据，绘制验证曲线
                val_file = os.path.join(results_path, 'validation.csv')
                if os.path.exists(val_file):
                    val_df = pd.read_csv(val_file)
                    ax2.clear()
                    ax2.plot(val_df['epoch'], val_df['validation_ap'], 'purple', label='Validation AP')
                    ax2.set_xlabel('Epoch')
                    ax2.set_ylabel('AP')
                    ax2.legend()
                    ax2.grid(True)
                    ax2.set_title('Validation Performance')
                
                plt.tight_layout()
                plt.draw()
                plt.pause(5)  # 每5秒更新一次
                
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(5)

if __name__ == "__main__":
    results_path = "./results/STEEL/ALL_3000_N_3000"
    plot_training_progress(results_path)