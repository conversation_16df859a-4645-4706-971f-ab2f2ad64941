using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Globalization;
using System.Runtime.InteropServices;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using Avalonia.Media.Immutable;
using Avalonia.Media.TextFormatting;
using ImageViewer.Drawing;

namespace ImageViewer.Controls;

public sealed class RenderView : Control
{
    public static readonly StyledProperty<Bitmap?> SourceProperty =
        AvaloniaProperty.Register<RenderView, Bitmap?>(nameof(Source));

    public Bitmap? Source
    {
        get => GetValue(SourceProperty);
        set => SetValue(SourceProperty, value);
    }

    public static readonly StyledProperty<bool> ShowPixelGridProperty =
        AvaloniaProperty.Register<RenderView, bool>(nameof(ShowPixelGrid), true);
    public bool ShowPixelGrid
    {
        get => GetValue(ShowPixelGridProperty);
        set => SetValue(ShowPixelGridProperty, value);
    }

    public static readonly StyledProperty<IBrush> GridLineBrushProperty =
        AvaloniaProperty.Register<RenderView, IBrush>(nameof(GridLineBrush),
            new SolidColorBrush(Color.FromArgb(0x96, 0x64, 0x64, 0x64)));
    public IBrush GridLineBrush
    {
        get => GetValue(GridLineBrushProperty);
        set => SetValue(GridLineBrushProperty, value);
    }

    // 以“设备像素”为单位配置线宽，自动换算到 DIP，避免高 DPI 下过粗/过细
    public static readonly StyledProperty<double> GridLineThicknessInDevicePixelsProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(GridLineThicknessInDevicePixels), 1.0);
    public double GridLineThicknessInDevicePixels
    {
        get => GetValue(GridLineThicknessInDevicePixelsProperty);
        set => SetValue(GridLineThicknessInDevicePixelsProperty, value);
    }
    
    public static readonly StyledProperty<IBrush> DefaultFillProperty =
        AvaloniaProperty.Register<RenderView, IBrush>(nameof(DefaultFill),
            new SolidColorBrush(Color.FromArgb(0x22,0x00,0xA2,0xFF)));
    public IBrush DefaultFill
    {
        get => GetValue(DefaultFillProperty);
        set => SetValue(DefaultFillProperty, value);
    }
    
    public static readonly StyledProperty<IBrush> DefaultStrokeProperty =
        AvaloniaProperty.Register<RenderView, IBrush>(nameof(DefaultStroke),
            Brushes.DodgerBlue);
    public IBrush DefaultStroke
    {
        get => GetValue(DefaultStrokeProperty);
        set => SetValue(DefaultStrokeProperty, value);
    }
    
    public static readonly StyledProperty<double> DefaultStrokeThicknessProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(DefaultStrokeThickness), 1.5);
    public double DefaultStrokeThickness 
    { 
        get => GetValue(DefaultStrokeThicknessProperty);
        set => SetValue(DefaultStrokeThicknessProperty, value);
    }
    
    public static readonly StyledProperty<ToolMode> ToolProperty =
        AvaloniaProperty.Register<RenderView, ToolMode>(nameof(Tool), ToolMode.None);
    public ToolMode Tool 
    { 
        get => GetValue(ToolProperty);
        set => SetValue(ToolProperty, value);
    }
    
    public List<IShape> Shapes { get; } = new();
    public double? SelectedAngleDeg => _active is ImageViewer.Drawing.RectShape rr ? rr.AngleDeg : null;
    
    // 缩放上限/下限
    public double MinScale { get; set; } = 0.05;
    public double MaxScale { get; set; } = 40.0;

    // 滚轮缩放倍率（每一档的乘子）
    public double WheelZoomStep { get; set; } = 1.1;

    // 默认光标
    private readonly Cursor _defaultCursor = new Cursor(StandardCursorType.Cross);
    
    // 右键拖拽时的状态
    private bool _isPanning;
    private Point _lastPointer;
    private Point _offset = new Point(0, 0);
    private double _scale = 1.0;
    
    private bool _drawing;
    private IShape? _active;
    private HitResult _hit = HitResult.None;
    private Point _startMPos;

    // 鼠标像素信息事件：在图像上移动时，通知外界当前坐标与RGB
    public event EventHandler<PixelInfoEventArgs>? PixelInfoChanged;

    public RenderView()
    {
        Focusable = true; // 获得焦点
        ClipToBounds = true; // 超出边界不绘制
        
        RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.LowQuality);
        
        // 鼠标光标
        Cursor = _defaultCursor;
        
        // 事件订阅（兼容性更好）
        AddHandler(PointerPressedEvent, OnPointerPressed, handledEventsToo: true);
        AddHandler(PointerReleasedEvent, OnPointerReleased, handledEventsToo: true);
        AddHandler(PointerMovedEvent, OnPointerMoved, handledEventsToo: true);
        AddHandler(PointerWheelChangedEvent, OnPointerWheelChanged, handledEventsToo: true);
        AddHandler(KeyDownEvent, OnKeyDown, handledEventsToo: true);
    }

    protected override void OnPropertyChanged(AvaloniaPropertyChangedEventArgs change)
    {
        base.OnPropertyChanged(change);
        if (change.Property == SourceProperty)
        {
            // 新图默认自适应“居中+适配最小边”
            FitImage();
            UpdateInterpolationMode();
        }
    }

    #region 键盘交互
    private void OnKeyDown(object? sender, KeyEventArgs e)
    {
        if (e.Key == Key.R) // R 键重置
        {
            FitImage();
            e.Handled = true;
        }

        if (e.Key == Key.D1)
        {
            Tool = ToolMode.Edit;
        }

        if (e.Key == Key.Delete)
        {
            if (_active != null)
            {
                Shapes.Remove(_active);
                _active = null;
                InvalidateVisual();
                e.Handled = true;
            }
        }
    }
    #endregion

    #region 鼠标交互
    private void OnPointerPressed(object? sender, PointerPressedEventArgs e)
    {
        if (Source is null) return;

        var vp = new Viewport(_scale, _offset);
        var vpos = e.GetPosition(this);
        
        var props = e.GetCurrentPoint(this).Properties;
        if (props.IsRightButtonPressed)
        {
            _isPanning = true;
            _lastPointer = vpos;

            Cursor = new Cursor(StandardCursorType.Hand);
            
            e.Pointer.Capture(this);
            e.Handled = true;
        }
        
        if (props.IsLeftButtonPressed && e.ClickCount == 2) // 左键双击：回到适配
        {
            FitImage();
            e.Handled = true;
        }
        
        if (props.IsLeftButtonPressed && e.ClickCount == 1)
        {
            this.Focus();
            var mpos = vp.ViewToImage(vpos);
            // 优先命中已有图形进行编辑（无需切换编辑模式）
            _hit = HitTest(mpos);
            if (_hit.Shape != null)
            {
                Console.WriteLine("[" + DateTime.Now.ToString("HH:mm:ss.fff") + ']'+ " Hit: " + _hit.Kind + _hit.HandleIndex);
                Select(_hit.Shape);
                if (_hit.Kind == HitKind.Handle && _active is ImageViewer.Drawing.RectShape rs0)
                {
                    rs0.BeginHandleDrag(_hit.HandleIndex);
                }
                _startMPos = mpos;
                e.Pointer.Capture(this);
                e.Handled = true;
                InvalidateVisual();
            }
            else
            {
                // 未命中则开始绘制新的矩形
                _startMPos = mpos;
                BeginNewShape(mpos);
                e.Pointer.Capture(this);
                e.Handled = true;
            }
        }
    }
    
    private void OnPointerMoved(object? sender, PointerEventArgs e)
    {
        var vp = new Viewport(_scale, _offset);
        var vpos = e.GetPosition(this);
        var mpos = vp.ViewToImage(vpos);

        // 实时采样像素并上报
        RaisePixelInfo(mpos);
        
        if (_isPanning)
        {
            var delta = vpos - _lastPointer;
            _lastPointer = vpos;

            _offset = new Point(_offset.X + delta.X, _offset.Y + delta.Y);
            InvalidateVisual();
            e.Handled = true;
        }
        
        if (_drawing)
        {
            UpdateNewShape(mpos, e.KeyModifiers);
            InvalidateVisual();
            e.Handled = true;
        }
        
        var props = e.GetCurrentPoint(this).Properties;
        if (_active != null && props.IsLeftButtonPressed && !_drawing)
        {
            if (_hit.Kind == HitKind.Body)
            {
                _active.Move(mpos - _startMPos);
            }
            else if (_hit.Kind == HitKind.Handle)
            {
                _active.MoveHandle(_hit.HandleIndex, mpos);
            }
            _startMPos = mpos;
            InvalidateVisual();
            e.Handled = true;
        }

        // 未按键时，根据命中结果更新光标
        if (!props.IsLeftButtonPressed && !_isPanning && !_drawing)
        {
            var h = HitTest(mpos);
            if (h.Kind == HitKind.Handle)
            {
                if (h.Shape is ShapeBase sb)
                {
                    foreach (var hd in sb.Handles)
                    {
                        if (hd is ShapeHandle sh && sh.Index == h.HandleIndex)
                        {
                            Cursor = sh.Cursor;
                            goto CursorDone;
                        }
                    }
                }
                // 后备
                Cursor = _defaultCursor;
            }
            else if (h.Kind == HitKind.Body)
            {
                Cursor = _defaultCursor;
            }
            else
            {
                Cursor = _defaultCursor;
            }
        CursorDone: ;
        }
    }
    
    private void OnPointerReleased(object? sender, PointerReleasedEventArgs e)
    {
        if (_isPanning)
        {
            _isPanning = false;
            if (e.Pointer.Captured == this)
                e.Pointer.Capture(null);
            Cursor = _defaultCursor;
            e.Handled = true;
        }

        if (_drawing)
        {
            CommitNewShape(new Viewport(_scale, _offset).ViewToImage(e.GetPosition(this)));
            if (e.Pointer.Captured == this)
            {
                e.Pointer.Capture(null);
                InvalidateVisual(); 
                e.Handled=true;
                return;
            }
        }

        if (_active != null)
        {
            if (_active is ImageViewer.Drawing.RectShape rs1)
            {
                rs1.EndHandleDrag();
            }
            _hit = HitResult.None;
            if (e.Pointer.Captured == this)
            {
                e.Pointer.Capture(null);
            }
        }
        e.Handled=true;
    }
    
    private void OnPointerWheelChanged(object? sender, PointerWheelEventArgs e)
    {
        if (Source is null) return;

        // 光标位置
        var p = e.GetPosition(this);

        // 计算新缩放
        double factor = e.Delta.Y > 0 ? WheelZoomStep : 1.0 / WheelZoomStep;
        double oldScale = _scale;
        double newScale = Math.Clamp(_scale * factor, MinScale, MaxScale);

        if (Math.Abs(newScale - oldScale) < 1e-6)
            return;

        // 保持光标下的图像点不漂移：offset' = p - (p - offset) * (s'/s)
        var sx = newScale / oldScale;
        _offset = new Point(
            p.X - (p.X - _offset.X) * sx,
            p.Y - (p.Y - _offset.Y) * sx
        );

        _scale = newScale;
        
        UpdateInterpolationMode();
        InvalidateVisual();
        e.Handled = true;
    }

    #endregion
    

    protected override void OnSizeChanged(SizeChangedEventArgs e)
    {
        base.OnSizeChanged(e);
        // 你也可以选择 Size 变化时保持当前视图；这里不自动Fit，除非首次加载。
        if (Source != null && _scale == 1.0 && _offset == default)
        {
            FitImage();
        }
        UpdateInterpolationMode();
    }

    private void FitImage()
    {
        if (Source is null || Bounds.Width <= 0 || Bounds.Height <= 0)
        {
            _scale = 1.0;
            _offset = new Point(0, 0);
            InvalidateVisual();
            return;
        }

        // 按较小的缩放适配到控件区域并居中
        double vw = Bounds.Width;
        double vh = Bounds.Height;
        double iw = Source.Size.Width;   // DIP
        double ih = Source.Size.Height;  // DIP

        double s = Math.Min(vw / iw, vh / ih);
        s = Math.Clamp(s, MinScale, MaxScale);

        _scale = s;
        double dx = (vw - iw * s) / 2.0;
        double dy = (vh - ih * s) / 2.0;
        _offset = new Point(dx, dy);

        InvalidateVisual();
    }

    public override void Render(DrawingContext context)
    {
        base.Render(context);

        DrawCheckerboard(context, Bounds, 18);
        
        if (Source is null) return;

        // 计算目标矩形：缩放 + 平移
        var iw = Source.Size.Width;
        var ih = Source.Size.Height;
        var dest = new Rect(_offset.X, _offset.Y, iw * _scale, ih * _scale);

        // 绘制图像（源矩形用原图大小）
        var src = new Rect(0, 0, iw, ih);
        
        if (_scale >= 9.0)
        {
            RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.None);
            context.DrawImage(Source, src, dest);
            if (ShowPixelGrid && _scale >= 10.0)
            {
                DrawPixelGrid(context, dest, (int)iw, (int)ih);
            }
        }
        else
        {
            RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.LowQuality);
            context.DrawImage(Source, src, dest);
        }
        
        var vp = new Viewport(_scale, _offset);
        // 仅绘制与视口相交的形状
        foreach (var s in Shapes)
        {
            if (!s.GetViewBounds(vp).Intersects(Bounds)) continue;
            s.Paint(context, vp);
        }

        DrawHud(context);
    }

    // 将图像坐标转换为像素并读取RGB，触发事件
    private void RaisePixelInfo(Point imgPos)
    {
        if (Source is null)
        {
            _lastPixel = new LastPixel { X = -1, Y = -1, R = 0, G = 0, B = 0, InBounds = false };
            PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(-1, -1, 0, 0, 0, false));
            InvalidateVisual();
            return;
        }

        // 将DIP坐标映射到实际像素（考虑位图DPI缩放）
        double scaleX = Source.PixelSize.Width / Source.Size.Width;
        double scaleY = Source.PixelSize.Height / Source.Size.Height;
        int px = (int)Math.Floor(imgPos.X * scaleX);
        int py = (int)Math.Floor(imgPos.Y * scaleY);

        bool inBounds = px >= 0 && py >= 0 && px < Source.PixelSize.Width && py < Source.PixelSize.Height;
        if (!inBounds)
        {
            _lastPixel = new LastPixel { X = px, Y = py, R = 0, G = 0, B = 0, InBounds = false };
            PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(px, py, 0, 0, 0, false));
            InvalidateVisual();
            return;
        }

        byte r = 0, g = 0, b = 0;
        // 通过 CopyPixels 读取单像素（BGRA 32bpp）
        var rect = new PixelRect(px, py, 1, 1);
        var buf = new byte[4];
        var handle = GCHandle.Alloc(buf, GCHandleType.Pinned);
        try
        {
            Source.CopyPixels(rect, handle.AddrOfPinnedObject(), 4, 4);
            b = buf[0];
            g = buf[1];
            r = buf[2];
        }
        finally
        {
            if (handle.IsAllocated) handle.Free();
        }

        _lastPixel = new LastPixel { X = px, Y = py, R = r, G = g, B = b, InBounds = true };
        PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(px, py, r, g, b, true));
        InvalidateVisual();
    }

    private struct LastPixel
    {
        public int X;
        public int Y;
        public byte R;
        public byte G;
        public byte B;
        public bool InBounds;
    }
    private LastPixel _lastPixel;

    private void DrawHud(DrawingContext context)
    {
        string angleText = SelectedAngleDeg is double a ? $"  Angle: {a:0.0}°" : string.Empty;

        // 选择用于显示的 (x,y)：若有选中矩形，则用其未旋转矩形的左上角；否则用鼠标像素坐标
        int hudX = _lastPixel.X;
        int hudY = _lastPixel.Y;
        if (_active is ImageViewer.Drawing.RectShape rs && Source != null)
        {
            // 将图像坐标映射为像素坐标
            double sx = Source.PixelSize.Width / Source.Size.Width;
            double sy = Source.PixelSize.Height / Source.Size.Height;
            hudX = (int)Math.Floor(rs.Rect.TopLeft.X * sx);
            hudY = (int)Math.Floor(rs.Rect.TopLeft.Y * sy);
        }

        string text = _lastPixel.InBounds
            ? $"(x:{hudX}, y:{hudY})  RGB: ({_lastPixel.R}, {_lastPixel.G}, {_lastPixel.B}){angleText}"
            : $"(x:{hudX}, y:{hudY})  RGB: N/A{angleText}";

        var fg = Brushes.White;
        var bg = new SolidColorBrush(Color.FromArgb(0x99, 0x00, 0x00, 0x00));
        var typeface = new Typeface("Segoe UI");
        double fontSize = 12;
        var ft = new FormattedText(text, CultureInfo.CurrentUICulture, FlowDirection.LeftToRight, typeface, fontSize, fg);
        // 固定高度的底栏，避免测量 API 差异
        double barHeight = 24;
        double y = Bounds.Height - barHeight;
        var rect = new Rect(0, y, Bounds.Width, barHeight);
        context.FillRectangle(bg, rect);
        // 文本位置：左边距 8，顶部内边距 5
        var textPos = new Point(8, y + 5);
        context.DrawText(ft, textPos);
    }

    public sealed class PixelInfoEventArgs : EventArgs
    {
        public int X { get; }
        public int Y { get; }
        public byte R { get; }
        public byte G { get; }
        public byte B { get; }
        public bool InBounds { get; }

        public PixelInfoEventArgs(int x, int y, byte r, byte g, byte b, bool inBounds)
        {
            X = x; Y = y; R = r; G = g; B = b; InBounds = inBounds;
        }
    }

    private void UpdateInterpolationMode()
    {
        var mode = (_scale > 10.0)
            ? BitmapInterpolationMode.None   // 放大：像素级
            : BitmapInterpolationMode.LowQuality;    // 其他：正常插值
        
        RenderOptions.SetBitmapInterpolationMode(this, mode);
    }

    #region 绘制ROI逻辑
    private void BeginNewShape(Point img)
    {
        _drawing = true;
        var s = new RectShape(new Rect(img, img))
        {
            Selected = true
        };
        Shapes.Add(s);
        _active = s;
    }
    private void UpdateNewShape(Point img, KeyModifiers mods)
    {
        if (_active is not RectShape rs) return;
        var r = Geom.NormalizeRect(_startMPos, img);
        rs.Rect = r;
        // 调试：打印矩形尺寸（图像/视图）
        var vp = new Viewport(_scale, _offset);
        var rv = vp.ImageToView(r);
        Console.WriteLine($"[DRAW] img:({r.X:F2},{r.Y:F2},{r.Width:F2},{r.Height:F2}) view:({rv.X:F1},{rv.Y:F1},{rv.Width:F1},{rv.Height:F1}) scale={_scale:F2}");
    }
    private void CommitNewShape(Point img)
    {
        _drawing = false;
        _active = null;
    }
    #endregion


    private void Select(IShape s)
    {
        foreach (var x in Shapes)
        {
            x.Selected = false;
            s.Selected = true;
            _active = s;
        }
    }

    

    private double DipToImg(double dip)
    {
        var rs = (VisualRoot as TopLevel)?.RenderScaling ?? 1.0;
        var dipPerDevicePx = 1.0 / rs;
        return (dip*dipPerDevicePx) / _scale;
    }
    private HitResult HitTest(Point img)
    {
        var vp = new Viewport(_scale, _offset);
        var view = Bounds;
        double tolImg = DipToImg(6);
        for (int i = Shapes.Count - 1; i >= 0; i--)
        {
            var sh = Shapes[i]; 
            if (!sh.GetViewBounds(vp).Inflate(8).Intersects(view)) continue;
            var hr = sh.HitTest(img, tolImg);
            if (hr.Kind != HitKind.None)
            {
                return hr;
            }
        }
        return HitResult.None;
    }
    
    private void DrawPixelGrid(DrawingContext ctx, Rect dest, int iw, int ih)
    {
        double renderScaling = (this.VisualRoot as TopLevel)?.RenderScaling ?? 1.0;
        double thicknessDIP = GridLineThicknessInDevicePixels / renderScaling;

        var pen = new Pen(GridLineBrush, thicknessDIP);

        // 仅绘制视口内的像素线，减少循环次数
        var view = Bounds.Intersect(dest);

        int x0 = Math.Max(0, (int)Math.Floor((view.X - dest.X) / _scale));
        int x1 = Math.Min(iw, (int)Math.Ceiling((view.Right - dest.X) / _scale));
        int y0 = Math.Max(0, (int)Math.Floor((view.Y - dest.Y) / _scale));
        int y1 = Math.Min(ih, (int)Math.Ceiling((view.Bottom - dest.Y) / _scale));

        // 像素对齐：把线的位置吸附到设备像素栅格，线更锐利
        static double Snap(double x, double rs) => Math.Round(x * rs) / rs;

        // 竖线（像素列分隔）
        for (int xi = x0; xi <= x1; xi++)
        {
            double x = dest.X + xi * _scale;
            double xs = Snap(x, renderScaling);
            ctx.DrawLine(pen, new Point(xs, view.Y), new Point(xs, view.Bottom));
        }

        // 横线（像素行分隔）
        for (int yi = y0; yi <= y1; yi++)
        {
            double y = dest.Y + yi * _scale;
            double ys = Snap(y, renderScaling);
            ctx.DrawLine(pen, new Point(view.X, ys), new Point(view.Right, ys));
        }
    }

    private void DrawCheckerboard(DrawingContext ctx, Rect bounds, int cell = 18)
    {
        var light = new SolidColorBrush(Color.FromRgb(50, 50, 50));
        var dark  = new SolidColorBrush(Color.FromRgb(35, 35, 35));
        for (int y = 0; y < bounds.Height; y += cell)
        {
            for (int x = 0; x < bounds.Width; x += cell)
            {
                bool odd = ((x / cell) + (y / cell)) % 2 == 1;
                ctx.FillRectangle(odd ? dark : light,
                    new Rect(x, y, Math.Min(cell, bounds.Width - x), Math.Min(cell, bounds.Height - y)));
            }
        }
    }
}