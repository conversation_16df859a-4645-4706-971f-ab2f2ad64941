{"Version": 1, "WorkspaceRootPath": "E:\\AvaloniaApplication9.9\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\imageview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\imageview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\controls\\renderview.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\controls\\renderview.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\imageview.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\imageview.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\mainwindow.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\mainwindow.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\galleryview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\galleryview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\shapehandle.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\shapehandle.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\shapes\\lineshape.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\shapes\\lineshape.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\shapes\\circleshape.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\shapes\\circleshape.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\viewport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\viewport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\shapebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\shapebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\shapes\\polygonshape.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\shapes\\polygonshape.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\enums\\toolmode.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\enums\\toolmode.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\enums\\shapekind.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\enums\\shapekind.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\enums\\handlerole.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\enums\\handlerole.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\imageviewer\\drawing\\enums\\hitkind.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\imageviewer\\drawing\\enums\\hitkind.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\inspectview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\inspectview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\services\\imagedrawingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\services\\imagedrawingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\viewmodels\\imageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\viewmodels\\imageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\projectview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\projectview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\viewmodels\\projectviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\viewmodels\\projectviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\viewmodels\\splitviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\viewmodels\\splitviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\converters\\tagidtoiconkindconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\converters\\tagidtoiconkindconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\models\\tagcategory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\models\\tagcategory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\services\\imageeditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\services\\imageeditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\galleryview.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\galleryview.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\viewmodels\\galleryviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\viewmodels\\galleryviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\splitview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\splitview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\viewmodels\\inspectviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\viewmodels\\inspectviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\trainview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\trainview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\viewmodels\\viewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\viewmodels\\viewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\models\\projectitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\models\\projectitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|e:\\avaloniaapplication9.9\\avaloniaapplication2\\views\\projectview.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E341CDBC-C91A-4626-96A1-231D14FE7235}|AvaloniaApplication2\\AvaloniaApplication2.csproj|solutionrelative:avaloniaapplication2\\views\\projectview.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.axaml", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\MainWindow.axaml", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\MainWindow.axaml", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\MainWindow.axaml", "RelativeToolTip": "AvaloniaApplication2\\Views\\MainWindow.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-14T05:20:11.858Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ImageView.axaml.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ImageView.axaml.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\ImageView.axaml.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ImageView.axaml.cs", "RelativeToolTip": "AvaloniaApplication2\\Views\\ImageView.axaml.cs", "ViewState": "AgIAAFEEAAAAAAAAAAAewFwEAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T08:39:23.373Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "RenderView.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Controls\\RenderView.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Controls\\RenderView.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Controls\\RenderView.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Controls\\RenderView.cs", "ViewState": "AgIAADcDAAAAAAAAAAAgwFQDAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:12:17.502Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "PolygonShape.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\PolygonShape.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\PolygonShape.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\PolygonShape.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\PolygonShape.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:05:00.978Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "ShapeBase.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeBase.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeBase.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeBase.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:05:06.009Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ShapeHandle.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeHandle.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeHandle.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeHandle.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\ShapeHandle.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:05:17.651Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "Viewport.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Viewport.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Viewport.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Viewport.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Viewport.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:05:33.269Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Program.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Program.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Program.cs", "RelativeToolTip": "AvaloniaApplication2\\Program.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAYwBYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T08:10:57.365Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "GalleryView.axaml", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\GalleryView.axaml", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\GalleryView.axaml", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\GalleryView.axaml", "RelativeToolTip": "AvaloniaApplication2\\Views\\GalleryView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-14T05:42:02.251Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ImageView.axaml", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ImageView.axaml", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\ImageView.axaml", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ImageView.axaml", "RelativeToolTip": "AvaloniaApplication2\\Views\\ImageView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-14T05:42:03.1Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ToolMode.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ToolMode.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ToolMode.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ToolMode.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ToolMode.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:04:53.756Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "ShapeKind.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ShapeKind.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ShapeKind.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ShapeKind.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\ShapeKind.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:04:51.136Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "HandleRole.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HandleRole.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HandleRole.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HandleRole.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HandleRole.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:04:48.569Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "HitKind.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HitKind.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HitKind.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HitKind.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Enums\\HitKind.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:04:45.609Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "LineShape.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\LineShape.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\LineShape.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\LineShape.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\LineShape.cs", "ViewState": "AgIAAFgAAAAAAAAAAAAYwG4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T07:03:02.344Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "CircleShape.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\CircleShape.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\CircleShape.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\CircleShape.cs", "RelativeToolTip": "AvaloniaApplication2\\ImageViewer\\Drawing\\Shapes\\CircleShape.cs", "ViewState": "AgIAAGYAAAAAAAAAAAAAwCIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T06:59:48.715Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "ImageViewModel.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\ImageViewModel.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ViewModels\\ImageViewModel.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\ImageViewModel.cs", "RelativeToolTip": "AvaloniaApplication2\\ViewModels\\ImageViewModel.cs", "ViewState": "AgIAAMEAAAAAAAAAAAAnwMwAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:41:57.113Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "ImageDrawingService.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Services\\ImageDrawingService.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Services\\ImageDrawingService.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Services\\ImageDrawingService.cs", "RelativeToolTip": "AvaloniaApplication2\\Services\\ImageDrawingService.cs", "ViewState": "AgIAAJUBAAAAAAAAAAAewJ4BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-18T10:00:17.469Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "InspectView.axaml", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\InspectView.axaml", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\InspectView.axaml", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\InspectView.axaml", "RelativeToolTip": "AvaloniaApplication2\\Views\\InspectView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-14T05:42:03.666Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "ProjectView.axaml", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ProjectView.axaml", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\ProjectView.axaml", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ProjectView.axaml", "RelativeToolTip": "AvaloniaApplication2\\Views\\ProjectView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-14T05:40:11.553Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "ProjectViewModel.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\ProjectViewModel.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ViewModels\\ProjectViewModel.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\ProjectViewModel.cs", "RelativeToolTip": "AvaloniaApplication2\\ViewModels\\ProjectViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:42:00.058Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "SplitViewModel.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\SplitViewModel.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ViewModels\\SplitViewModel.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\SplitViewModel.cs", "RelativeToolTip": "AvaloniaApplication2\\ViewModels\\SplitViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T07:21:14.22Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "TagIdToIconKindConverter.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Converters\\TagIdToIconKindConverter.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Converters\\TagIdToIconKindConverter.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Converters\\TagIdToIconKindConverter.cs", "RelativeToolTip": "AvaloniaApplication2\\Converters\\TagIdToIconKindConverter.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAswDcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-15T07:13:21.814Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "GalleryView.axaml.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\GalleryView.axaml.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\GalleryView.axaml.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\GalleryView.axaml.cs", "RelativeToolTip": "AvaloniaApplication2\\Views\\GalleryView.axaml.cs", "ViewState": "AgIAAKEBAAAAAAAAAAAswBcAAABOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-15T03:35:04.713Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "ImageEditService.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Services\\ImageEditService.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Services\\ImageEditService.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Services\\ImageEditService.cs", "RelativeToolTip": "AvaloniaApplication2\\Services\\ImageEditService.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAIwB8AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-15T04:31:40.265Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "TagCategory.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Models\\TagCategory.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Models\\TagCategory.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Models\\TagCategory.cs", "RelativeToolTip": "AvaloniaApplication2\\Models\\TagCategory.cs", "ViewState": "AgIAABIAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-15T05:43:38.867Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "GalleryViewModel.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\GalleryViewModel.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ViewModels\\GalleryViewModel.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\GalleryViewModel.cs", "RelativeToolTip": "AvaloniaApplication2\\ViewModels\\GalleryViewModel.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAAwG0AAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:41:56.231Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "SplitView.axaml", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\SplitView.axaml", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\SplitView.axaml", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\SplitView.axaml", "RelativeToolTip": "AvaloniaApplication2\\Views\\SplitView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-14T07:21:10.257Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "ViewModelBase.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\ViewModelBase.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ViewModels\\ViewModelBase.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\ViewModelBase.cs", "RelativeToolTip": "AvaloniaApplication2\\ViewModels\\ViewModelBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:41:59.373Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ViewModels\\MainWindowViewModel.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "AvaloniaApplication2\\ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAnwEsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:20:09.765Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "InspectViewModel.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\InspectViewModel.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\ViewModels\\InspectViewModel.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\ViewModels\\InspectViewModel.cs", "RelativeToolTip": "AvaloniaApplication2\\ViewModels\\InspectViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:41:57.956Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "TrainView.axaml", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\TrainView.axaml", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\TrainView.axaml", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\TrainView.axaml", "RelativeToolTip": "AvaloniaApplication2\\Views\\TrainView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-14T07:30:24.147Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "ProjectItem.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Models\\ProjectItem.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Models\\ProjectItem.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Models\\ProjectItem.cs", "RelativeToolTip": "AvaloniaApplication2\\Models\\ProjectItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:41:53.006Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "ProjectView.axaml.cs", "DocumentMoniker": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ProjectView.axaml.cs", "RelativeDocumentMoniker": "AvaloniaApplication2\\Views\\ProjectView.axaml.cs", "ToolTip": "E:\\AvaloniaApplication9.9\\AvaloniaApplication2\\Views\\ProjectView.axaml.cs", "RelativeToolTip": "AvaloniaApplication2\\Views\\ProjectView.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-14T05:39:58.147Z"}]}]}]}