using Avalonia;
using Avalonia.Input;
using System.Linq;
using AvaloniaApplication2.ROI.Shapes;

namespace AvaloniaApplication2.ROI.Tools
{
    // Drag to create axis-aligned rotated-rectangle (angle=0). Rotation can be adjusted via rotate handle.
    public class ToolRotatedRectangle : ToolBase
    {
        private RoiRotatedRectangle? _creating;
        private Point _start;
        private ToolPointer? _proxyPointer; // transient delegate for editing existing ROIs

        public override void OnMouseDown(Point p, PointerPointProperties props, int clickCount, bool isRightClick)
        {
            if (Engine is null) return;
            // If clicking on an existing ROI/handle, delegate to pointer tool so handles work in drawing mode
            var hit = Engine.Shapes.LastOrDefault(s => s.HitTest(p) >= 0);
            if (hit != null)
            {
                _proxyPointer ??= new ToolPointer();
                _proxyPointer.Attach(Engine);
                _proxyPointer.OnMouseDown(p, props, clickCount, isRightClick);
                return;
            }
            if (props.IsLeftButtonPressed)
            {
                _start = p;
                _creating = new RoiRotatedRectangle(center: p, width: 0, height: 0, angleDeg: 0);
                // 初始化样式
                _creating.Stroke = Engine.DefaultStroke;
                _creating.StrokeThickness = Engine.DefaultStrokeThickness;
                Engine.Shapes.Add(_creating);
                _creating.IsSelected = true;
                Engine.NotifyShapesChanged();
            }
        }

        public override void OnMouseMove(Point p, bool isLeftDown)
        {
            if (_proxyPointer != null)
            {
                _proxyPointer.OnMouseMove(p, isLeftDown);
                return;
            }
            if (Engine is null || _creating is null || !isLeftDown) return;
            var p0 = _start; var p1 = p;
            var center = new Point((p0.X + p1.X)/2.0, (p0.Y + p1.Y)/2.0);
            var w = System.Math.Abs(p1.X - p0.X);
            var h = System.Math.Abs(p1.Y - p0.Y);
            _creating.Center = center;
            _creating.AngleDeg = 0;
            _creating.Width = w;
            _creating.Height = h;
            Engine.NotifyShapesChanged();
        }

        public override void OnMouseUp(Point p)
        {
            if (_proxyPointer != null)
            {
                _proxyPointer.OnMouseUp(p);
                _proxyPointer = null;
                Engine.NotifyShapesChanged();
                return;
            }
            if (_creating != null)
            {
                Engine.NotifyShapesChanged();
                _creating = null;
            }
        }
    }
}
