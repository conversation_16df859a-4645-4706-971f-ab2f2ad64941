        using System;
using Avalonia;
using AvaloniaApplication2.ROI;

namespace AvaloniaApplication2.ROI.Shapes
{
    public class RoiRectangle : RoiShape
    {
        public Rect Rect;
        // Rotation handle distance (image pixels)
        public const double RotationHandleOffset = 20.0;
        // Resize-session state to enforce fixed-opposite-corner with optional constant aspect ratio
        private int _resizeHandle = -1;
        private double _resizeAspect = 1.0; // width/height captured at drag start
        private Point _fixedCorner; // the opposite corner kept fixed during diagonal resize
        private bool _constrainAspect = false;
        private Point _startCorner; // 拖拽开始时，当前被拖动角点的位置
        private Rect _sessionRect;  // 开始拖拽时的矩形，稳定几何参考
        private Rect _sessionBounds; // 会话时的图像边界
        private bool _dragging;     // 是否处于会话拖拽中
        private Point _anchorWorld; // 世界坐标下的锚点位置（保持不漂移）

        private enum AnchorType { None, Corner_TL, Corner_TR, Corner_BL, Corner_BR, Edge_Left, Edge_Right, Edge_Top, Edge_Bottom }

        private static AnchorType AnchorFromHandle(int handle)
        {
            return handle switch
            {
                1 => AnchorType.Corner_BR,
                3 => AnchorType.Corner_BL,
                5 => AnchorType.Corner_TL,
                7 => AnchorType.Corner_TR,
                2 => AnchorType.Edge_Bottom,
                6 => AnchorType.Edge_Top,
                4 => AnchorType.Edge_Left,
                8 => AnchorType.Edge_Right,
                _ => AnchorType.None
            };
        }

        private static Point GetAnchorWorld(Rect rect, AnchorType t)
        {
            var c = rect.Center;
            return t switch
            {
                AnchorType.Corner_TL => rect.TopLeft,
                AnchorType.Corner_TR => rect.TopRight,
                AnchorType.Corner_BL => rect.BottomLeft,
                AnchorType.Corner_BR => rect.BottomRight,
                AnchorType.Edge_Left => new Point(rect.Left, (rect.Top + rect.Bottom) / 2),
                AnchorType.Edge_Right => new Point(rect.Right, (rect.Top + rect.Bottom) / 2),
                AnchorType.Edge_Top => new Point((rect.Left + rect.Right) / 2, rect.Top),
                AnchorType.Edge_Bottom => new Point((rect.Left + rect.Right) / 2, rect.Bottom),
                _ => c
            };
        }

        // Start a resize session: specify which handle and whether to constrain aspect ratio (e.g., Shift pressed)
        public void BeginResizeSession(int handleIndex, bool constrainAspect)
        {
            _resizeHandle = handleIndex;
            _constrainAspect = constrainAspect;
            _sessionRect = Rect; // 冻结会话起始矩形
            _resizeAspect = Math.Max(1e-6, _sessionRect.Width / Math.Max(1e-6, _sessionRect.Height));
            // 若调用方未设置，会被 ToolPointer 的重载版本覆盖
            if (_sessionBounds.Width <= 0 || _sessionBounds.Height <= 0)
                _sessionBounds = new Rect(double.NegativeInfinity, double.NegativeInfinity, double.PositiveInfinity, double.PositiveInfinity);
            _dragging = true;
            _anchorWorld = GetAnchorWorld(_sessionRect, AnchorFromHandle(handleIndex));
            _fixedCorner = handleIndex switch
            {
                1 => _sessionRect.BottomRight,
                3 => _sessionRect.BottomLeft,
                5 => _sessionRect.TopLeft,
                7 => _sessionRect.TopRight,
                _ => default
            };
            _startCorner = handleIndex switch
            {
                1 => _sessionRect.TopLeft,
                3 => _sessionRect.TopRight,
                5 => _sessionRect.BottomRight,
                7 => _sessionRect.BottomLeft,
                2 => new Point((_sessionRect.Left+_sessionRect.Right)/2, _sessionRect.Top),
                4 => new Point(_sessionRect.Right, (_sessionRect.Top+_sessionRect.Bottom)/2),
                6 => new Point((_sessionRect.Left+_sessionRect.Right)/2, _sessionRect.Bottom),
                8 => new Point(_sessionRect.Left, (_sessionRect.Top+_sessionRect.Bottom)/2),
                _ => default
            };
        }

        // 由 ToolPointer 在按下时调用，注入图像边界
        public void BeginResizeSession(int handleIndex, bool constrainAspect, Rect bounds)
        {
            _sessionBounds = bounds;
            BeginResizeSession(handleIndex, constrainAspect);
        }

        public RoiRectangle(Rect rect)
        {
            Rect = rect;
            Type = RoiShapeType.Rectangle;
        }

        public override Rect GetBounds() => Rect;

        public override void Move(Vector delta)
        {
            Rect = new Rect(Rect.Position + delta, Rect.Size);
        }

        public override int HitTest(Point p)
        {
            // Handles order: TL(1) TM(2) TR(3) RM(4) BR(5) BM(6) BL(7) LM(8) ROTATE(9)
            var r = Rect;
            double hs = 6;
            Point TL = r.TopLeft, TR = r.TopRight, BR = r.BottomRight, BL = r.BottomLeft;
            Point TM = new Point((r.Left + r.Right)/2, r.Top);
            Point RM = new Point(r.Right, (r.Top + r.Bottom)/2);
            Point BM = new Point((r.Left + r.Right)/2, r.Bottom);
            Point LM = new Point(r.Left, (r.Top + r.Bottom)/2);
            // Rotation handle: above top-middle by a fixed offset; must be checked即使在矩形外
            var rot = new Point(TM.X, TM.Y - RotationHandleOffset);
            if (new Rect(rot.X - hs, rot.Y - hs, hs * 2, hs * 2).Contains(p)) return 9;
            if (!Rect.Inflate(6).Contains(p)) return -1;
            if (new Rect(TL.X-hs, TL.Y-hs, hs*2, hs*2).Contains(p)) return 1;
            if (new Rect(TM.X-hs, TM.Y-hs, hs*2, hs*2).Contains(p)) return 2;
            if (new Rect(TR.X-hs, TR.Y-hs, hs*2, hs*2).Contains(p)) return 3;
            if (new Rect(RM.X-hs, RM.Y-hs, hs*2, hs*2).Contains(p)) return 4;
            if (new Rect(BR.X-hs, BR.Y-hs, hs*2, hs*2).Contains(p)) return 5;
            if (new Rect(BM.X-hs, BM.Y-hs, hs*2, hs*2).Contains(p)) return 6;
            if (new Rect(BL.X-hs, BL.Y-hs, hs*2, hs*2).Contains(p)) return 7;
            if (new Rect(LM.X-hs, LM.Y-hs, hs*2, hs*2).Contains(p)) return 8;
            if (Rect.Contains(p)) return 0;
            return -1;
        }

        public override void MoveHandleTo(Point p, int h)
        {
            const double MinSize = 2.0; // 最小尺寸，避免翻转/跳变
            // 仅响应当前会话的手柄，避免在拖拽中途切换造成跳变
            if (_resizeHandle != h)
                return;

            double l = Rect.Left, t = Rect.Top, rgt = Rect.Right, btm = Rect.Bottom;

            // 简单、稳定的角点缩放：
            // 固定对角 O，从 p 生成 Q，Q 在边界内；
            // 再按句柄方向对 dx/dy 强制最小绝对值并限制在边界允许范围内；
            // 最后由 O 与 Q' 构造矩形。
            static double Clamp(double v, double lo, double hi) => Math.Min(Math.Max(v, lo), hi);
            Point ClampPoint(Point pt, Rect bounds) => new Point(Clamp(pt.X, bounds.Left, bounds.Right), Clamp(pt.Y, bounds.Top, bounds.Bottom));
            AnchorType anchorType = AnchorType.None;
            AnchorType? anchorTypeEffective = null;
            switch (h)
            {
                case 1: // TL，固定 O=BR
                {
                    var O = _fixedCorner; // 固定角不 Clamp，避免基准漂移
                    var q = ClampPoint(p, _sessionBounds);
                    // 可用空间与有效最小值
                    double availLeft = O.X - _sessionBounds.Left;   // ≥0
                    double availUp   = O.Y - _sessionBounds.Top;    // ≥0
                    double minX = Math.Min(MinSize, Math.Max(0, availLeft));
                    double minY = Math.Min(MinSize, Math.Max(0, availUp));
                    // 期望方向：dx<0, dy<0
                    double dxMin = -availLeft;   // 最远到边界（负）
                    double dxMax = -minX;        // 至少保持有效最小宽度
                    if (dxMin > dxMax) dxMax = dxMin; // 当空间不足时，锁到边界
                    double dyMin = -availUp;     // 最远到边界（负）
                    double dyMax = -minY;
                    if (dyMin > dyMax) dyMax = dyMin;
                    double dx = Clamp(q.X - O.X, dxMin, dxMax);
                    double dy = Clamp(q.Y - O.Y, dyMin, dyMax);
                    var Qp = new Point(O.X + dx, O.Y + dy);
                    l = Math.Min(O.X, Qp.X); t = Math.Min(O.Y, Qp.Y); rgt = Math.Max(O.X, Qp.X); btm = Math.Max(O.Y, Qp.Y);
                    anchorType = AnchorType.Corner_BR;
                    break;
                }
                case 2: // Top 边（锚：Bottom）——允许越界翻面，保持逻辑锚边不动
                {
                    var q = ClampPoint(p, _sessionBounds);
                    double anchor = _sessionRect.Bottom;
                    bool crossed = q.Y > anchor;
                    if (!crossed)
                    {
                        t = Clamp(q.Y, _sessionBounds.Top, _sessionBounds.Bottom);
                        btm = Clamp(anchor, _sessionBounds.Top, _sessionBounds.Bottom);
                    }
                    else
                    {
                        t = Clamp(anchor, _sessionBounds.Top, _sessionBounds.Bottom);
                        btm = Clamp(q.Y, _sessionBounds.Top, _sessionBounds.Bottom);
                    }
                    anchorType = AnchorType.Edge_Bottom;
                    anchorTypeEffective = crossed ? AnchorType.Edge_Top : AnchorType.Edge_Bottom;
                    break;
                }
                case 3: // TR，固定 O=BL
                {
                    var O = _fixedCorner; // 固定角不 Clamp
                    var q = ClampPoint(p, _sessionBounds);
                    double availRight = _sessionBounds.Right - O.X; // ≥0
                    double availUp    = O.Y - _sessionBounds.Top;   // ≥0
                    double minX = Math.Min(MinSize, Math.Max(0, availRight));
                    double minY = Math.Min(MinSize, Math.Max(0, availUp));
                    // 期望方向：dx>0, dy<0
                    double dxMin = minX;             // 至少保持有效最小宽度
                    double dxMax = availRight;        // 最远到右边界
                    if (dxMin > dxMax) dxMin = dxMax; // 空间不足：锁到边界
                    double dyMin = -availUp;          // 最远到上边界（负）
                    double dyMax = -minY;             // 保持有效最小高度（负）
                    if (dyMin > dyMax) dyMin = dyMax;
                    double dx = Clamp(q.X - O.X, dxMin, dxMax);
                    double dy = Clamp(q.Y - O.Y, dyMin, dyMax);
                    var Qp = new Point(O.X + dx, O.Y + dy);
                    l = Math.Min(O.X, Qp.X); t = Math.Min(O.Y, Qp.Y); rgt = Math.Max(O.X, Qp.X); btm = Math.Max(O.Y, Qp.Y);
                    anchorType = AnchorType.Corner_BL;
                    break;
                }
                case 4: // Right 边（锚：Left）——允许越界翻面
                {
                    var q = ClampPoint(p, _sessionBounds);
                    double anchor = _sessionRect.Left;
                    bool crossed = q.X < anchor;
                    if (!crossed)
                    {
                        l = Clamp(anchor, _sessionBounds.Left, _sessionBounds.Right);
                        rgt = Clamp(q.X, _sessionBounds.Left, _sessionBounds.Right);
                    }
                    else
                    {
                        l = Clamp(q.X, _sessionBounds.Left, _sessionBounds.Right);
                        rgt = Clamp(anchor, _sessionBounds.Left, _sessionBounds.Right);
                    }
                    anchorType = AnchorType.Edge_Left;
                    anchorTypeEffective = crossed ? AnchorType.Edge_Right : AnchorType.Edge_Left;
                    break;
                }
                case 5: // BR，固定 O=TL
                {
                    var O = _fixedCorner; // 固定角不 Clamp
                    var q = ClampPoint(p, _sessionBounds);
                    double availRight = _sessionBounds.Right - O.X; // ≥0
                    double availDown  = _sessionBounds.Bottom - O.Y; // ≥0
                    double minX = Math.Min(MinSize, Math.Max(0, availRight));
                    double minY = Math.Min(MinSize, Math.Max(0, availDown));
                    // 期望方向：dx>0, dy>0
                    double dxMin = minX;
                    double dxMax = availRight;
                    if (dxMin > dxMax) dxMin = dxMax;
                    double dyMin = minY;
                    double dyMax = availDown;
                    if (dyMin > dyMax) dyMin = dyMax;
                    double dx = Clamp(q.X - O.X, dxMin, dxMax);
                    double dy = Clamp(q.Y - O.Y, dyMin, dyMax);
                    var Qp = new Point(O.X + dx, O.Y + dy);
                    l = Math.Min(O.X, Qp.X); t = Math.Min(O.Y, Qp.Y); rgt = Math.Max(O.X, Qp.X); btm = Math.Max(O.Y, Qp.Y);
                    anchorType = AnchorType.Corner_TL;
                    break;
                }
                case 6: // Bottom 边（锚：Top）——允许越界翻面
                {
                    var q = ClampPoint(p, _sessionBounds);
                    double anchor = _sessionRect.Top;
                    bool crossed = q.Y < anchor;
                    if (!crossed)
                    {
                        t = Clamp(anchor, _sessionBounds.Top, _sessionBounds.Bottom);
                        btm = Clamp(q.Y, _sessionBounds.Top, _sessionBounds.Bottom);
                    }
                    else
                    {
                        t = Clamp(q.Y, _sessionBounds.Top, _sessionBounds.Bottom);
                        btm = Clamp(anchor, _sessionBounds.Top, _sessionBounds.Bottom);
                    }
                    anchorType = AnchorType.Edge_Top;
                    anchorTypeEffective = crossed ? AnchorType.Edge_Bottom : AnchorType.Edge_Top;
                    break;
                }
                case 7: // BL，固定 O=TR
                {
                    var O = _fixedCorner; // 固定角不 Clamp
                    var q = ClampPoint(p, _sessionBounds);
                    double availLeft = O.X - _sessionBounds.Left;  // ≥0
                    double availDown = _sessionBounds.Bottom - O.Y; // ≥0
                    double minX = Math.Min(MinSize, Math.Max(0, availLeft));
                    double minY = Math.Min(MinSize, Math.Max(0, availDown));
                    // 期望方向：dx<0, dy>0
                    double dxMin = -availLeft;  // 最远到左边界
                    double dxMax = -minX;       // 保持有效最小宽度
                    if (dxMin > dxMax) dxMax = dxMin;
                    double dyMin = minY;
                    double dyMax = availDown;
                    if (dyMin > dyMax) dyMin = dyMax;
                    double dx = Clamp(q.X - O.X, dxMin, dxMax);
                    double dy = Clamp(q.Y - O.Y, dyMin, dyMax);
                    var Qp = new Point(O.X + dx, O.Y + dy);
                    l = Math.Min(O.X, Qp.X); t = Math.Min(O.Y, Qp.Y); rgt = Math.Max(O.X, Qp.X); btm = Math.Max(O.Y, Qp.Y);
                    anchorType = AnchorType.Corner_TR;
                    break;
                }
                case 8: // Left 边（锚：Right）——允许越界翻面
                {
                    var q = ClampPoint(p, _sessionBounds);
                    double anchor = _sessionRect.Right;
                    bool crossed = q.X > anchor;
                    if (!crossed)
                    {
                        l = Clamp(q.X, _sessionBounds.Left, _sessionBounds.Right);
                        rgt = Clamp(anchor, _sessionBounds.Left, _sessionBounds.Right);
                    }
                    else
                    {
                        l = Clamp(anchor, _sessionBounds.Left, _sessionBounds.Right);
                        rgt = Clamp(q.X, _sessionBounds.Left, _sessionBounds.Right);
                    }
                    anchorType = AnchorType.Edge_Right;
                    anchorTypeEffective = crossed ? AnchorType.Edge_Left : AnchorType.Edge_Right;
                    break;
                }
                default:
                    return;
            }

            // 平滑处理：极小变化直接忽略，降低临界抖动
            const double SmoothEps = 0.5; // 经验阈值，可按需要微调
            double newW = rgt - l, newH = btm - t;
            if (Math.Abs(newW - Rect.Width) < SmoothEps && Math.Abs(newH - Rect.Height) < SmoothEps)
                return;
            // 归一化、并对齐世界坐标下的锚点，保持锚不漂移
            var nl = Math.Min(l, rgt);
            var nr = Math.Max(l, rgt);
            var nt = Math.Min(t, btm);
            var nb = Math.Max(t, btm);
            var newRect = new Rect(new Point(nl, nt), new Point(nr, nb));
            if (_dragging && anchorType != AnchorType.None)
            {
                var eff = anchorTypeEffective ?? anchorType;
                var newAnchorWorld = GetAnchorWorld(newRect, eff);
                var delta = _anchorWorld - newAnchorWorld;
                newRect = new Rect(newRect.Position + delta, newRect.Size);
            }
            Rect = newRect;
        }

        public void EndResizeSession()
        {
            _resizeHandle = -1;
            _constrainAspect = false;
            _startCorner = default;
            _sessionBounds = default;
            _dragging = false;
        }

        private static Rect Normalize(double l,double t,double r,double b)
        {
            var x1 = Math.Min(l, r); var x2 = Math.Max(l, r);
            var y1 = Math.Min(t, b); var y2 = Math.Max(t, b);
            return new Rect(new Point(x1,y1), new Point(x2,y2));
        }
    }
}
