using System;
using Avalonia;
using AvaloniaApplication2.ROI;

namespace AvaloniaApplication2.ROI.Shapes
{
    public class RoiRotatedRectangle : RoiShape
    {
        public Point Center { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
        public double AngleDeg { get; set; }
        // 0=Top edge, 1=Right, 2=Bottom, 3=Left; controls where rotation handle is attached
        public int RotationHandleEdge { get; set; } = 0;
        // Rotation handle distance (image pixels)
        public const double RotationHandleOffset = 20.0;

        // Resize session state
        private int _resizeHandle = -1;
        private Rect _sessionBounds;
        private Point _sessionCenter;     // 会话初始中心
        private double _sessionW;         // 会话初始宽
        private double _sessionH;         // 会话初始高
        private double _sessionAngleDeg;  // 会话初始角度
        private const double MinSize = 1.0;   // 最小半宽/半高的一半（总宽/高为其两倍）
        private const double SmoothEps = 0.5; // 死区抑抖

        public RoiRotatedRectangle(Point center, double width, double height, double angleDeg = 0)
        {
            Center = center;
            Width = width;
            Height = height;
            AngleDeg = angleDeg;
            Type = RoiShapeType.RotatedRectangle;
        }

        public override Rect GetBounds()
        {
            var c = GetCorners();
            double minX = Math.Min(Math.Min(c[0].X, c[1].X), Math.Min(c[2].X, c[3].X));
            double maxX = Math.Max(Math.Max(c[0].X, c[1].X), Math.Max(c[2].X, c[3].X));
            double minY = Math.Min(Math.Min(c[0].Y, c[1].Y), Math.Min(c[2].Y, c[3].Y));
            double maxY = Math.Max(Math.Max(c[0].Y, c[1].Y), Math.Max(c[2].Y, c[3].Y));
            return new Rect(new Point(minX, minY), new Point(maxX, maxY));
        }

        public override void Move(Vector delta)
        {
            Center += delta;
        }

        public Point[] GetCorners()
        {
            double hw = Width / 2.0, hh = Height / 2.0;
            var rad = AngleDeg * Math.PI / 180.0;
            var cos = Math.Cos(rad); var sin = Math.Sin(rad);
            Point LocalToWorld(double x, double y)
            {
                return new Point(
                    Center.X + x * cos - y * sin,
                    Center.Y + x * sin + y * cos);
            }
            return new[]
            {
                LocalToWorld(-hw,-hh), // TL (local)
                LocalToWorld( hw,-hh), // TR
                LocalToWorld( hw, hh), // BR
                LocalToWorld(-hw, hh), // BL
            };
        }

        private Point WorldToLocal(Point p)
        {
            var rad = AngleDeg * Math.PI / 180.0;
            var cos = Math.Cos(rad); var sin = Math.Sin(rad);
            var v = p - Center;
            return new Point(v.X * cos + v.Y * sin, -v.X * sin + v.Y * cos);
        }

        public override int HitTest(Point p)
        {
            var lp = WorldToLocal(p);
            double hw = Width / 2.0, hh = Height / 2.0;
            double tol = 8; // 基础手柄/边容差，提升可点击性
            // rotation handle (index 9): attached to fixed edge defined by RotationHandleEdge
            Point rotW = GetRotateHandleWorld();
            var distRot = Math.Sqrt((p.X - rotW.X) * (p.X - rotW.X) + (p.Y - rotW.Y) * (p.Y - rotW.Y));
            if (distRot <= Math.Max(10, tol)) return 9; // 旋转手柄更宽容
            // corners (1..4): TL, TR, BR, BL
            if (Distance(lp, new Point(-hw, -hh)) <= tol) return 1;
            if (Distance(lp, new Point( hw, -hh)) <= tol) return 2;
            if (Distance(lp, new Point( hw,  hh)) <= tol) return 3;
            if (Distance(lp, new Point(-hw,  hh)) <= tol) return 4;
            // edges (5..8): top,right,bottom,left midpoints
            if (Math.Abs(lp.Y + hh) <= tol && Math.Abs(lp.X) <= hw) return 5;
            if (Math.Abs(lp.X - hw) <= tol && Math.Abs(lp.Y) <= hh) return 6;
            if (Math.Abs(lp.Y - hh) <= tol && Math.Abs(lp.X) <= hw) return 7;
            if (Math.Abs(lp.X + hw) <= tol && Math.Abs(lp.Y) <= hh) return 8;
            // inside
            if (Math.Abs(lp.X) <= hw && Math.Abs(lp.Y) <= hh) return 0;
            return -1;
        }

        public Point GetRotateHandleWorld(double offset = RotationHandleOffset)
        {
            double hw = Width / 2.0, hh = Height / 2.0;
            // Edge midpoints in local space
            Point midLocal = RotationHandleEdge switch
            {
                0 => new Point(0, -hh),          // top mid
                1 => new Point(hw, 0),           // right mid
                2 => new Point(0, hh),           // bottom mid
                3 => new Point(-hw, 0),          // left mid
                _ => new Point(0, -hh)
            };
            // outward normal in local space
            Vector nLocal = RotationHandleEdge switch
            {
                0 => new Vector(0, -1),
                1 => new Vector(1, 0),
                2 => new Vector(0, 1),
                3 => new Vector(-1, 0),
                _ => new Vector(0, -1)
            };
            var local = new Point(midLocal.X + nLocal.X * offset, midLocal.Y + nLocal.Y * offset);
            var rad = AngleDeg * Math.PI / 180.0;
            var cos = Math.Cos(rad); var sin = Math.Sin(rad);
            return new Point(Center.X + local.X * cos - local.Y * sin, Center.Y + local.X * sin + local.Y * cos);
        }

        public override void MoveHandleTo(Point p, int handleIndex)
        {
            // For body move, immediate
            if (handleIndex == 0)
            {
                Center = p;
                return;
            }

            // ensure handle is consistent in session
            if (_resizeHandle != -1 && _resizeHandle != handleIndex) return;

            // clamp pointer in world to session bounds during resize to avoid double clamping elsewhere
            static double Clamp(double v, double lo, double hi) => Math.Min(Math.Max(v, lo), hi);
            Point ClampPoint(Point pt, Rect b) => new Point(Clamp(pt.X, b.Left, b.Right), Clamp(pt.Y, b.Top, b.Bottom));
            var pw = (_resizeHandle == -1) ? p : ClampPoint(p, _sessionBounds);
            // 使用会话初始几何将世界坐标映射为局部坐标，保证固定对角及稳定性
            Point WorldToLocalSession(Point wp)
            {
                var rad = _sessionAngleDeg * Math.PI / 180.0;
                var cos = Math.Cos(rad); var sin = Math.Sin(rad);
                var v = wp - _sessionCenter;
                return new Point(v.X * cos + v.Y * sin, -v.X * sin + v.Y * cos);
            }
            var lp = WorldToLocalSession(pw);

            double hw0 = _sessionW / 2.0, hh0 = _sessionH / 2.0;
            bool isCorner = handleIndex is 1 or 2 or 3 or 4;
            if (isCorner)
            {
                // 角点：固定对角点，双轴缩放，并保持 anchor 不变（local 空间下）
                int sx = (handleIndex == 2 || handleIndex == 3) ? +1 : -1; // TR/BR x正
                int sy = (handleIndex == 3 || handleIndex == 4) ? +1 : -1; // BR/BL y正
                var anchor = new Point(-sx * hw0, -sy * hh0); // local anchor

                // 目标角（local）按最小尺寸约束
                double hw = Math.Max(MinSize, Math.Abs(lp.X - anchor.X));
                double hh = Math.Max(MinSize, Math.Abs(lp.Y - anchor.Y));
                var newCorner = new Point(sx * hw, sy * hh);

                // 平滑死区：若变化很小则忽略
                if (Math.Abs(hw * 2.0 - Width) < SmoothEps && Math.Abs(hh * 2.0 - Height) < SmoothEps)
                    return;

                // 新中心（local）：anchor 与 newCorner 的中点
                var newCenterLocal = new Point((anchor.X + newCorner.X) / 2.0, (anchor.Y + newCorner.Y) / 2.0);
                var rad = _sessionAngleDeg * Math.PI / 180.0;
                var cos = Math.Cos(rad); var sin = Math.Sin(rad);
                var off = new Vector(newCenterLocal.X * cos - newCenterLocal.Y * sin, newCenterLocal.X * sin + newCenterLocal.Y * cos);
                Center = _sessionCenter + off;
                Width = hw * 2.0;
                Height = hh * 2.0;
            }
            else
            {
                // 边手柄：单轴缩放，并移动中心以保持对边不动
                double hw = hw0, hh = hh0;
                Point newCenterLocal = new Point(0, 0);
                switch (handleIndex)
                {
                    case 5: // top edge (local -Y)
                        hh = Math.Max(MinSize, Math.Abs(lp.Y));
                        newCenterLocal = new Point(0, -(hh - hh0));
                        break;
                    case 6: // right edge (local +X)
                        hw = Math.Max(MinSize, Math.Abs(lp.X));
                        newCenterLocal = new Point(+(hw - hw0), 0);
                        break;
                    case 7: // bottom edge (local +Y)
                        hh = Math.Max(MinSize, Math.Abs(lp.Y));
                        newCenterLocal = new Point(0, +(hh - hh0));
                        break;
                    case 8: // left edge (local -X)
                        hw = Math.Max(MinSize, Math.Abs(lp.X));
                        newCenterLocal = new Point(-(hw - hw0), 0);
                        break;
                    default:
                        return;
                }
                if (Math.Abs(hw * 2.0 - Width) < SmoothEps && Math.Abs(hh * 2.0 - Height) < SmoothEps)
                    return;
                var rad = _sessionAngleDeg * Math.PI / 180.0;
                var cos = Math.Cos(rad); var sin = Math.Sin(rad);
                var off = new Vector(newCenterLocal.X * cos - newCenterLocal.Y * sin, newCenterLocal.X * sin + newCenterLocal.Y * cos);
                Center = _sessionCenter + off;
                Width = hw * 2.0;
                Height = hh * 2.0;
            }
        }

        public override void Rotate(Point current, Point previous)
        {
            var v0 = previous - Center;
            var v1 = current - Center;
            var a0 = Math.Atan2(v0.Y, v0.X);
            var a1 = Math.Atan2(v1.Y, v1.X);
            AngleDeg += (a1 - a0) * 180.0 / Math.PI;
        }

        private static double Distance(in Point a, in Point b)
        {
            double dx = a.X - b.X, dy = a.Y - b.Y;
            return Math.Sqrt(dx * dx + dy * dy);
        }

        // Resize session API for pointer tool to inject image bounds and lock handle
        public void BeginResizeSession(int handleIndex, Rect bounds)
        {
            _resizeHandle = handleIndex;
            _sessionBounds = bounds;
            // 记录会话初始几何，避免在缩放过程中基准漂移导致“飞走”现象
            _sessionCenter = Center;
            _sessionW = Math.Max(MinSize * 2, Width);
            _sessionH = Math.Max(MinSize * 2, Height);
            _sessionAngleDeg = AngleDeg;
        }

        public void EndResizeSession()
        {
            _resizeHandle = -1;
            _sessionBounds = new Rect(0, 0, 0, 0);
            _sessionCenter = default;
            _sessionW = _sessionH = 0;
            _sessionAngleDeg = 0;
        }
    }
}
