<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style Selector="RadioButton.SidebarButton">
        <Setter Property="Padding" Value="15,10"/>
        <Setter Property="Margin" Value="5,0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Background="Transparent">
                    <Border Name="underline"
                            BorderBrush="{DynamicResource SystemControlForegroundAccentBrush}"
                            BorderThickness="0,0,0,2"
                            IsVisible="False"
                            VerticalAlignment="Bottom"/>
                    <ContentPresenter Content="{TemplateBinding Content}"
                                      Padding="{TemplateBinding Padding}"
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                </Panel>
            </ControlTemplate>
        </Setter>
    </Style>

    <Style Selector="RadioButton.SidebarButton:checked /template/ Border#underline">
        <Setter Property="IsVisible" Value="True"/>
    </Style>

    <Style Selector="RadioButton.SidebarButton:pointerover">
        <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundListLowBrush}"/>
    </Style>

    <Style Selector="RadioButton.SidebarButton TextBlock">
        <Setter Property="Margin" Value="15,0,0,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <Style Selector="RadioButton.SidebarButton PathIcon">
        <Setter Property="Width" Value="20"/>
        <Setter Property="Height" Value="20"/>
    </Style>
</Styles>
