using System;
using System.Collections.ObjectModel;
using System.Linq;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;
using AvaloniaApplication2.Models;
using AvaloniaApplication2.ROI.Engine;
using AvaloniaApplication2.ROI;
using AvaloniaApplication2.ROI.Shapes;
using AvaloniaApplication2.ROI.Tools;
using System.Collections.Generic;

namespace AvaloniaApplication2.Services
{
    public enum EditHandleKind { None, Move, Rotate, N, S, E, W, NE, NW, SE, SW }

    public class ImageDrawingService
    {
        public RoiEditorEngine RoiEngine { get; } = new();
        // Simple undo stack for basic actions (to be expanded during ROI engine migration)
        private readonly Stack<Action> _undoStack = new();
        // 旧 Rois 已废弃，全部改为使用 RoiEngine.Shapes
        public ToolMode Mode { get; private set; } = ToolMode.Pan;

        private IBrush _stroke = Brushes.Lime;
        public IBrush Stroke
        {
            get => _stroke;
            set
            {
                if (_stroke == value) return;
                _stroke = value ?? Brushes.Lime;
                // 同步到引擎默认样式
                RoiEngine.DefaultStroke = _stroke;
                // 立即应用到当前选中 ROI
                var sel = RoiEngine.Shapes.LastOrDefault(s => s.IsSelected);
                if (sel != null)
                {
                    sel.Stroke = _stroke;
                }
                InvalidateRequested?.Invoke();
            }
        }

        private double _strokeThickness = 1.0;
        public double StrokeThickness
        {
            get => _strokeThickness;
            set
            {
                if (Math.Abs(_strokeThickness - value) < 0.0001) return;
                _strokeThickness = Math.Max(0.1, value);
                // 同步到引擎默认样式
                RoiEngine.DefaultStrokeThickness = _strokeThickness;
                // 立即应用到当前选中 ROI
                var sel = RoiEngine.Shapes.LastOrDefault(s => s.IsSelected);
                if (sel != null)
                {
                    sel.StrokeThickness = _strokeThickness;
                }
                InvalidateRequested?.Invoke();
            }
        }

        private PolylineRoi? _activePolyline;
        private RectRoi? _activeRect;
        private RotatedRectRoi? _activeRotRectCreate;
        private Point? _rectCreateStart;
        private IRoi? _dragSelection;
        private Point _lastImagePt;
        private bool _isDragging;

        // Per-image ROI storage
        // 每张图片对应一套 ROI（新引擎形状）
        private readonly Dictionary<string, List<RoiShape>> _roiStore = new();
        private string _currentImageKey = string.Empty;

        // External hooks to pan the main ScrollViewer
        public Func<Vector, bool>? RequestPanBy; // returns true if handled
        public Action? InvalidateRequested; // notify view to refresh visuals if needed
        public Action<string>? GalleryRefreshRequested; // 通知图库刷新当前图片缩略图

        public ImageDrawingService()
        {
            // 订阅引擎形状变更，驱动视图重绘与图库刷新
            RoiEngine.ShapesChanged += () =>
            {
                InvalidateRequested?.Invoke();
                if (!string.IsNullOrWhiteSpace(_currentImageKey))
                    GalleryRefreshRequested?.Invoke(_currentImageKey);
            };
        }

        // simple UI-thread throttle for redraw to boost responsiveness
        private DateTime _lastInvalidateTs = DateTime.MinValue;
        private void RequestInvalidateThrottled(int minMs = 12)
        {
            var now = DateTime.UtcNow;
            if ((now - _lastInvalidateTs).TotalMilliseconds >= minMs)
            {
                _lastInvalidateTs = now;
                InvalidateRequested?.Invoke();
            }
        }

        public void SetMode(ToolMode mode)
        {
            Mode = mode;
            _activePolyline = null;
            _activeRect = null;
            _activeRotRectCreate = null;
            _rectCreateStart = null;
            _dragSelection = null;

            // Map to new engine tools
            switch (Mode)
            {
                case ToolMode.Rectangle:
                    RoiEngine.SetTool(new ToolRectangle());
                    break;
                case ToolMode.Circle:
                    // For now, use the same tool as rectangle since both are closed shapes
                    RoiEngine.SetTool(new ToolRectangle());
                    break;
                case ToolMode.Line:
                    // For now, use the polygon tool but with a special flag for line mode
                    RoiEngine.SetTool(new ToolPolygon());
                    break;
                case ToolMode.Pencil:
                case ToolMode.Polygon:
                    RoiEngine.SetTool(new ToolPolygon());
                    break;
                case ToolMode.Pan:
                default:
                    RoiEngine.SetTool(new ToolPointer());
                    break;
            }
        }

        public void ResetState()
        {
            _activePolyline = null;
            _activeRect = null;
            _activeRotRectCreate = null;
            _rectCreateStart = null;
            _dragSelection = null;
            _isDragging = false;
        }

        public void SwitchImage(string key)
        {
            // 保存当前图像的形状
            if (!string.IsNullOrWhiteSpace(_currentImageKey))
            {
                _roiStore[_currentImageKey] = RoiEngine.Shapes.ToList();
            }

            // 切换到新图像并恢复该图像的形状
            _currentImageKey = key ?? string.Empty;
            RoiEngine.Shapes.Clear();
            if (!string.IsNullOrWhiteSpace(_currentImageKey) && _roiStore.TryGetValue(_currentImageKey, out var list))
            {
                foreach (var s in list)
                    RoiEngine.Shapes.Add(s);
            }
            ResetState();
            RequestInvalidateThrottled();
        }

        public void SetImageBounds(Rect bounds)
        {
            RoiEngine.ImageBounds = bounds;
        }

        public void DeleteSelected()
        {
            var target = RoiEngine.Shapes.LastOrDefault(r => r.IsSelected);
            if (target != null)
            {
                var idx = RoiEngine.Shapes.IndexOf(target);
                _undoStack.Push(() =>
                {
                    if (idx >= 0 && idx <= RoiEngine.Shapes.Count) RoiEngine.Shapes.Insert(idx, target);
                    foreach (var r in RoiEngine.Shapes) r.IsSelected = false;
                    target.IsSelected = true;
                    RequestInvalidateThrottled();
                });
                RoiEngine.Shapes.Remove(target);
                RequestInvalidateThrottled();
            }
        }

        /// <summary>
        /// Clears all shapes from the current image
        /// </summary>
        public void ClearShapes()
        {
            if (RoiEngine.Shapes.Count == 0) return;
            
            // Save current shapes for undo
            var shapesToRemove = RoiEngine.Shapes.ToList();
            _undoStack.Push(() =>
            {
                foreach (var shape in shapesToRemove)
                {
                    RoiEngine.Shapes.Add(shape);
                }
                RequestInvalidateThrottled();
            });
            
            // Clear all shapes
            RoiEngine.Shapes.Clear();
            // Also clear stored shapes for current image to avoid reappearing on image switch
            if (!string.IsNullOrWhiteSpace(_currentImageKey))
            {
                _roiStore[_currentImageKey] = new List<RoiShape>();
            }
            // Reset transient state
            ResetState();
            RequestInvalidateThrottled();
        }

        public void Undo()
        {
            if (_undoStack.Count > 0)
            {
                var act = _undoStack.Pop();
                act?.Invoke();
            }
        }

        public void PointerPressed(Point imagePt, PointerPointProperties props, int clickCount, bool isRightClick)
        {
            _lastImagePt = imagePt;
            RoiEngine.PointerPressed(imagePt, props, clickCount, isRightClick);
            InvalidateRequested?.Invoke();
        }

        public void PointerMoved(Point imagePt, bool isLeftDown)
        {
            var delta = imagePt - _lastImagePt;
            _lastImagePt = imagePt;
            RoiEngine.PointerMoved(imagePt, isLeftDown);
            RequestInvalidateThrottled();
        }

        public void PointerReleased(Point imagePt)
        {
            RoiEngine.PointerReleased(imagePt);
            RequestInvalidateThrottled();
        }

        public void CompletePolygon()
        {
            if (_activePolyline != null && _activePolyline.Points.Count >= 3)
            {
                var first = _activePolyline.Points[0];
                var last = _activePolyline.Points[^1];
                // If not already closed, append first to close shape
                if (last != first)
                {
                    _activePolyline.Points.Add(first);
                }
                _activePolyline.IsClosed = true;
            }
            _activePolyline = null;
            RequestInvalidateThrottled();
        }

        public void CancelActive()
        {
            _isDragging = false;
            _dragSelection = null;
            _activeRect = null;
            _activePolyline = null;
            _activeRotRectCreate = null;
            _rectCreateStart = null;
            _editingRotRect = null;
            _rectEditKind = RectEditKind.None;
            InvalidateRequested?.Invoke();
        }

        private bool Contains(IRoi roi, Point p)
        {
            switch (roi)
            {
                case RectRoi rr:
                    // Allow selecting the border with some tolerance
                    var inflated = rr.Rect.Inflate(Math.Max(3, rr.StrokeThickness / 2 + 2));
                    return inflated.Contains(p);
                case PolylineRoi pl:
                    var bounds = pl.GetBounds();
                    if (!bounds.Inflate(6).Contains(p)) return false;
                    // simple distance to segments (works for open/closed)
                    for (int i = 1; i < pl.Points.Count; i++)
                    {
                        if (DistanceToSegment(p, pl.Points[i - 1], pl.Points[i]) <= Math.Max(6, pl.StrokeThickness + 2)) return true;
                    }
                    // If closed, also consider inside area as hit
                    if (pl.IsClosed && pl.Points.Count >= 3)
                    {
                        if (PointInPolygon(p, pl.Points)) return true;
                    }
                    return false;
                case RoiRotatedRectangle rr:
                    // 统一用手柄命中逻辑，覆盖：角点/边/旋转/内部（Move）
                    return HitTestRectHandle(rr, p) != RectEditKind.None;
                default:
                    return false;
            }
        }

        // ===== Rotated rectangle editing =====
        private enum RectEditKind { None, Move, Rotate, N, S, E, W, NE, NW, SE, SW }
        private RectEditKind _rectEditKind;
        private RoiRotatedRectangle? _editingRotRect;
        private Point _pressImagePt;
        private Point _pressLocalPt;
        private double _pressAngle;
        private double _pressW, _pressH;
        private Point _pressCenter;

        private void BeginRectEdit(RoiRotatedRectangle rr, Point imagePt)
        {
            _editingRotRect = rr;
            // 按下时对手柄判定更宽松，优先进入手柄编辑而非移动
            _rectEditKind = HitTestRectHandle(rr, imagePt, preferHandles: true);
            _pressImagePt = imagePt;
            _pressLocalPt = WorldToLocal(rr, imagePt);
            _pressAngle = rr.AngleDeg;
            _pressW = rr.Width; _pressH = rr.Height;
            _pressCenter = rr.Center;
        }

        private void ContinueRectEdit(Point imagePt)
        {
            if (_editingRotRect == null) return;
            var rr = _editingRotRect;
            switch (_rectEditKind)
            {
                case RectEditKind.Move:
                    // 按下点到当前点的增量用于移动
                    rr.Center += (imagePt - _pressImagePt);
                    _pressImagePt = imagePt;
                    break;
                case RectEditKind.Rotate:
                    var v0 = _pressImagePt - rr.Center;
                    var v1 = imagePt - rr.Center;
                    var a0 = Math.Atan2(v0.Y, v0.X);
                    var a1 = Math.Atan2(v1.Y, v1.X);
                    rr.AngleDeg = _pressAngle + (a1 - a0) * 180.0 / Math.PI;
                    // 不更新 _pressImagePt，保持相对初始按下角度
                    break;
                case RectEditKind.W:
                case RectEditKind.NE:
                case RectEditKind.NW:
                case RectEditKind.SE:
                case RectEditKind.SW:
                    // 统一在局部坐标系下处理
                    var lp = WorldToLocal(rr, imagePt);
                    var hw0 = _pressW / 2.0; var hh0 = _pressH / 2.0;
                    // 记录按下时的角标号
                    bool isCorner = _rectEditKind == RectEditKind.NE || _rectEditKind == RectEditKind.NW || _rectEditKind == RectEditKind.SE || _rectEditKind == RectEditKind.SW;

                    if (isCorner)
                    {
                        // 锚点为对角点（保持不动）
                        int sx = (_rectEditKind == RectEditKind.NE || _rectEditKind == RectEditKind.SE) ? +1 : -1; // 拖拽点 X 符号
                        int sy = (_rectEditKind == RectEditKind.SE || _rectEditKind == RectEditKind.SW) ? +1 : -1; // 拖拽点 Y 符号
                        var anchor = new Point(-sx * hw0, -sy * hh0);
                        var dragVec0 = new Vector(sx * hw0 - anchor.X, sy * hh0 - anchor.Y); // 初始对角向量
                        var dragVec = new Vector(lp.X - anchor.X, lp.Y - anchor.Y);
                        var len0 = Math.Sqrt(dragVec0.X * dragVec0.X + dragVec0.Y * dragVec0.Y);
                        var len1 = Math.Max(0.1, Math.Sqrt(dragVec.X * dragVec.X + dragVec.Y * dragVec.Y));
                        double s = Math.Max(0.05, len1 / Math.Max(0.1, len0)); // 等比例缩放系数
                        var hw = Math.Max(1, hw0 * s);
                        var hh = Math.Max(1, hh0 * s);
                        // 新角点（拖拽点）
                        var newCorner = new Point(sx * hw, sy * hh);
                        // 新中心 = 锚点与新角点的中点（局部坐标，相对按下时中心）
                        var newCenterLocal = new Point((anchor.X + newCorner.X) / 2.0, (anchor.Y + newCorner.Y) / 2.0);
                        // 映射回世界坐标：原中心 + 旋转向量
                        rr.Center = _pressCenter + LocalToWorldVec(rr, new Vector(newCenterLocal.X, newCenterLocal.Y));
                        rr.Width = hw * 2.0;
                        rr.Height = hh * 2.0;
                    }
                    else
                    {
                        // 边手柄：只改变一个维度，对边保持不动
                        double hw = hw0, hh = hh0;
                        Point newCenterLocal = new Point(0, 0);
                        if (_rectEditKind == RectEditKind.E || _rectEditKind == RectEditKind.W)
                        {
                            // X 方向变化
                            if (_rectEditKind == RectEditKind.E) hw = Math.Max(0.5, Math.Abs(lp.X));
                            else hw = Math.Max(0.5, Math.Abs(lp.X));
                            // 对边固定 => 中心朝拖拽方向移动一半
                            double sign = _rectEditKind == RectEditKind.E ? +1 : -1;
                            double dw = hw - hw0;
                            newCenterLocal = new Point(sign * dw, 0);
                        }
                        else if (_rectEditKind == RectEditKind.N || _rectEditKind == RectEditKind.S)
                        {
                            // Y 方向变化
                            if (_rectEditKind == RectEditKind.S) hh = Math.Max(0.5, Math.Abs(lp.Y));
                            else hh = Math.Max(0.5, Math.Abs(lp.Y));
                            double sign = _rectEditKind == RectEditKind.S ? +1 : -1;
                            double dh = hh - hh0;
                            newCenterLocal = new Point(0, sign * dh);
                        }
                        // 将局部中心偏移映射到世界并应用：原中心 + 旋转向量
                        rr.Center = _pressCenter + LocalToWorldVec(rr, new Vector(newCenterLocal.X, newCenterLocal.Y));
                        rr.Width = hw * 2.0;
                        rr.Height = hh * 2.0;
                    }
                    break;
            }
            RequestInvalidateThrottled();
        }

        private static Vector LocalToWorldVec(RoiRotatedRectangle rr, Vector v)
        {
            var rad = rr.AngleDeg * Math.PI / 180.0;
            var cos = Math.Cos(rad); var sin = Math.Sin(rad);
            return new Vector(v.X * cos - v.Y * sin, v.X * sin + v.Y * cos);
        }

        private RectEditKind HitTestRectHandle(RoiRotatedRectangle rr, Point p, bool preferHandles = false)
        {
            var lp = WorldToLocal(rr, p);
            var hw = rr.Width / 2.0; var hh = rr.Height / 2.0;
            double tolBase = Math.Max(6, rr.StrokeThickness + 4);
            double tolHandles = preferHandles ? tolBase + 10 : tolBase;
            double tolInside = tolBase;
            // corners
            if (Distance(lp, new Point(-hw, -hh)) <= tolHandles) return RectEditKind.NW;
            if (Distance(lp, new Point(hw, -hh)) <= tolHandles) return RectEditKind.NE;
            if (Distance(lp, new Point(hw, hh)) <= tolHandles) return RectEditKind.SE;
            if (Distance(lp, new Point(-hw, hh)) <= tolHandles) return RectEditKind.SW;
            // edges
            if (Math.Abs(lp.Y + hh) <= tolHandles && Math.Abs(lp.X) <= hw) return RectEditKind.N;
            if (Math.Abs(lp.Y - hh) <= tolHandles && Math.Abs(lp.X) <= hw) return RectEditKind.S;
            if (Math.Abs(lp.X - hw) <= tolHandles && Math.Abs(lp.Y) <= hh) return RectEditKind.E;
            if (Math.Abs(lp.X + hw) <= tolHandles && Math.Abs(lp.Y) <= hh) return RectEditKind.W;
            // 旋转手柄：位于“长边”的中点外侧，沿短轴法线方向外延。将容差略微缩小，避免覆盖边中点。
            double tolRotate = Math.Max(4, tolHandles * 0.8);
            if (NearRotateHandle(rr, p, tolRotate)) return RectEditKind.Rotate;
            // inside -> move
            if (Math.Abs(lp.X) <= hw && Math.Abs(lp.Y) <= hh) return RectEditKind.Move;
            return RectEditKind.None;
        }

        public EditHandleKind GetHoverHandleKind(Point imagePt)
        {
            // 优先：对已选中项做更精细的手柄判断；否则：取最上层命中项
            var topSelected = RoiEngine.Shapes.LastOrDefault(s => s.IsSelected);
            RoiShape? target = topSelected ?? RoiEngine.Shapes.LastOrDefault(s => s.HitTest(imagePt) >= 0);
            if (target == null) return EditHandleKind.None;

            if (target is RoiRotatedRectangle rr)
            {
                var kind = HitTestRectHandle(rr, imagePt);
                return kind switch
                {
                    RectEditKind.Move => EditHandleKind.Move,
                    RectEditKind.Rotate => EditHandleKind.Rotate,
                    RectEditKind.N => EditHandleKind.N,
                    RectEditKind.S => EditHandleKind.S,
                    RectEditKind.E => EditHandleKind.E,
                    RectEditKind.W => EditHandleKind.W,
                    RectEditKind.NE => EditHandleKind.NE,
                    RectEditKind.NW => EditHandleKind.NW,
                    RectEditKind.SE => EditHandleKind.SE,
                    RectEditKind.SW => EditHandleKind.SW,
                    _ => EditHandleKind.None
                };
            }
            if (target is RoiRectangle r)
            {
                int h = r.HitTest(imagePt);
                return h switch
                {
                    0 => EditHandleKind.Move,
                    1 => EditHandleKind.NW,
                    2 => EditHandleKind.N,
                    3 => EditHandleKind.NE,
                    4 => EditHandleKind.E,
                    5 => EditHandleKind.SE,
                    6 => EditHandleKind.S,
                    7 => EditHandleKind.SW,
                    8 => EditHandleKind.W,
                    9 => EditHandleKind.Rotate,
                    _ => EditHandleKind.None
                };
            }
            if (target is RoiPolygon pg)
            {
                int h = pg.HitTest(imagePt);
                if (h == 0) return EditHandleKind.Move; // edge/inside
                if (h > 0) return EditHandleKind.Move; // vertex drag uses move cursor
                return EditHandleKind.None;
            }
            return EditHandleKind.None;
        }

        private static bool PointInRotatedRect(RoiRotatedRectangle rr, Point p)
        {
            var lp = WorldToLocal(rr, p);
            var hw = rr.Width / 2.0; var hh = rr.Height / 2.0;
            return Math.Abs(lp.X) <= hw && Math.Abs(lp.Y) <= hh;
        }

        private static bool NearRotRectEdge(RoiRotatedRectangle rr, Point p, double tol)
        {
            var lp = WorldToLocal(rr, p);
            var hw = rr.Width / 2.0; var hh = rr.Height / 2.0;
            if (Math.Abs(Math.Abs(lp.X) - hw) <= tol && Math.Abs(lp.Y) <= hh + tol) return true;
            if (Math.Abs(Math.Abs(lp.Y) - hh) <= tol && Math.Abs(lp.X) <= hw + tol) return true;
            return false;
        }

        private static Point WorldToLocal(RoiRotatedRectangle rr, Point p)
        {
            var v = p - rr.Center;
            var rad = -rr.AngleDeg * Math.PI / 180.0;
            var cos = Math.Cos(rad); var sin = Math.Sin(rad);
            return new Point(v.X * cos - v.Y * sin, v.X * sin + v.Y * cos);
        }

        private static bool PointInPolygon(Point p, IEnumerable<Point> pts)
        {
            bool inside = false;
            Point? prev = null;
            foreach (var curr in pts)
            {
                if (prev == null) { prev = curr; continue; }
                var a = prev.Value; var b = curr;
                if (((a.Y > p.Y) != (b.Y > p.Y)) &&
                    (p.X < (b.X - a.X) * (p.Y - a.Y) / ((b.Y - a.Y) == 0 ? 1e-6 : (b.Y - a.Y)) + a.X))
                {
                    inside = !inside;
                }
                prev = curr;
            }
            return inside;
        }

        private static double DistanceToSegment(Point p, Point a, Point b)
        {
            var ab = b - a; var ap = p - a;
            var t = (ab.X * ap.X + ab.Y * ap.Y) / (ab.X * ab.X + ab.Y * ab.Y + 1e-6);
            t = Math.Clamp(t, 0, 1);
            var proj = a + ab * t;
            var d = proj - p;
            return Math.Sqrt(d.X * d.X + d.Y * d.Y);
        }

        private static bool NearRotateHandle(RoiRotatedRectangle rr, Point p, double tol)
        {
            // Use fixed-edge rotation handle position for accurate hit test
            var rhWorld = rr.GetRotateHandleWorld(18);
            return Distance(p, rhWorld) <= tol;
        }

        private static Point LocalToWorld(RoiRotatedRectangle rr, Point local)
        {
            var rad = rr.AngleDeg * Math.PI / 180.0;
            var cos = Math.Cos(rad); var sin = Math.Sin(rad);
            return new Point(
                rr.Center.X + local.X * cos - local.Y * sin,
                rr.Center.Y + local.X * sin + local.Y * cos);
        }

        private static double Distance(Point a, Point b)
        {
            var d = b - a;
            return Math.Sqrt(d.X * d.X + d.Y * d.Y);
        }
    }
}
