using System;
using System.IO;
using OpenCvSharp;
using System.Security.Cryptography;
using System.Text;

namespace AvaloniaApplication2.Services
{
    public interface IImageEditService
    {
        // factor 1.0 = no change
        // Brightness: factor > 1.0 is brighter, < 1.0 is darker
        // Contrast: factor > 1.0 higher contrast, < 1.0 lower contrast
        string AdjustBrightness(string inputPath, float factor);
        string AdjustContrast(string inputPath, float factor);
        // Opacity: 1.0 = opaque, 0.0 = fully transparent (adds alpha channel if missing)
        string AdjustOpacity(string inputPath, float alphaFactor);
    }

    public class ImageEditService : IImageEditService
    {
        public string AdjustBrightness(string inputPath, float factor)
        {
            return Process(inputPath, (src) =>
            {
                // alpha = 1 (no contrast change), beta controls brightness
                double alpha = 1.0;
                // Map factor to beta roughly: 1.0 => 0, 1.1 => +25, 0.9 => -25
                double beta = (factor - 1.0) * 250.0 / 1.0; // tweakable scaling
                var dst = new Mat();
                src.ConvertTo(dst, src.Type(), alpha, beta);
                return dst;
            }, $"brightness_{factor:F3}");
        }

        public string AdjustContrast(string inputPath, float factor)
        {
            return Process(inputPath, (src) =>
            {
                // alpha controls contrast, beta = 0 (no brightness shift)
                double alpha = Math.Max(0.0, factor);
                double beta = 0.0;
                var dst = new Mat();
                src.ConvertTo(dst, src.Type(), alpha, beta);
                return dst;
            }, $"contrast_{factor:F3}");
        }

        public string AdjustOpacity(string inputPath, float alphaFactor)
        {
            alphaFactor = Math.Clamp(alphaFactor, 0f, 1f);
            return Process(inputPath, (src) =>
            {
                // Ensure 4-channel BGRA for alpha manipulation
                Mat bgra = new();
                if (src.Channels() == 4)
                {
                    bgra = src.Clone();
                }
                else
                {
                    Cv2.CvtColor(src, bgra, ColorConversionCodes.BGR2BGRA);
                }

                // Split channels and scale alpha
                Mat[] channels = Cv2.Split(bgra);
                // alpha originally 0..255, scale by factor
                var alpha = channels[3];
                alpha.ConvertTo(alpha, alpha.Type(), alphaFactor, 0);
                channels[3] = alpha;
                Mat dst = new();
                Cv2.Merge(channels, dst);
                foreach (var c in channels) c.Dispose();
                if (bgra != src) bgra.Dispose();
                return dst;
            }, $"opacity_{alphaFactor:F3}");
        }

        private static string Process(string inputPath, Func<Mat, Mat> transform, string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(inputPath) || !File.Exists(inputPath))
                return inputPath;

            try
            {
                using var src = Cv2.ImRead(inputPath, ImreadModes.Unchanged);
                if (src.Empty()) return inputPath;

                using var dst = transform(src);

                var ext = Path.GetExtension(inputPath);
                var tempDir = Path.Combine(Path.GetTempPath(), "AvaloniaApp_Thumbs");
                Directory.CreateDirectory(tempDir);
                // include operation and factor in cache key to avoid stale image with same path
                var stableName = ComputeStableName(inputPath + "|" + cacheKey) + ext;
                var outputPath = Path.Combine(tempDir, stableName);
                Cv2.ImWrite(outputPath, dst);
                return outputPath;
            }
            catch
            {
                return inputPath;
            }
        }

        private static string ComputeStableName(string inputPath)
        {
            using var sha1 = SHA1.Create();
            var bytes = Encoding.UTF8.GetBytes(inputPath);
            var hash = sha1.ComputeHash(bytes);
            var sb = new StringBuilder(hash.Length * 2);
            foreach (var b in hash) sb.Append(b.ToString("x2"));
            return sb.ToString();
        }
    }
}
