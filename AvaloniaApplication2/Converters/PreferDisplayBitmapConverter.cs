using System;
using System.Collections.Generic;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media.Imaging;

namespace AvaloniaApplication2.Converters
{
    // Returns a Bitmap created from first non-empty path among values
    public class PreferDisplayBitmapConverter : IMultiValueConverter
    {
        public static readonly PreferDisplayBitmapConverter Instance = new PreferDisplayBitmapConverter();

        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            foreach (var v in values)
            {
                if (v is string s && !string.IsNullOrWhiteSpace(s))
                {
                    try { return new Bitmap(s); } catch { }
                }
            }
            return null;
        }
    }
}
