using System;
using System.Globalization;
using Avalonia.Data.Converters;

namespace AvaloniaApplication2.Converters
{
    public sealed class IntGreaterThanZeroToBoolConverter : IValueConverter
    {
        public static readonly IntGreaterThanZeroToBoolConverter Instance = new();

        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is int i) return i > 0;
            if (value is long l) return l > 0;
            if (value is string s && int.TryParse(s, out var n)) return n > 0;
            return false;
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotSupportedException();
        }
    }
}
