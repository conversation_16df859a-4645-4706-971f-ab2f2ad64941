using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using Avalonia.Data.Converters;
using AvaloniaApplication2.Models;
using IconPacks.Avalonia.Material;


namespace AvaloniaApplication2.Converters
{
    // MultiBinding: [0]=int tagId, [1]=ObservableCollection<TagCategory> tags
    public sealed class TagIdToIconKindConverter : IMultiValueConverter
    {
        public static readonly TagIdToIconKindConverter Instance = new();

        // Removed reflection-based enum resolution. We'll return PackIconMaterialKind directly.

        private static string NormalizeIconName(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return "TagOutline";
            var key = name.Trim();
            var lower = key.ToLowerInvariant();

            // Map common status aliases to Material icon names
            // Good
            if (lower is "ok" or "pass" or "passed" or "success" or "良好" or "通过" or "合格" or "正常")
                return "ThumbUp";
            // Bad
            if (lower is "ng" or "fail" or "failed" or "error" or "异常" or "不合格" or "失败")
                return "ThumbDown";

            // Keep original for already-correct names like ThumbUp/ThumbDown/TagOutline/etc.
            return key;
        }

        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            if (values == null || values.Count < 2) return null;
            var tagId = values[0] is int i ? i : (values[0] is string s && int.TryParse(s, out var n) ? n : 0);
            if (values[1] is ObservableCollection<TagCategory> tags)
            {
                var tag = tags.FirstOrDefault(t => t.Id == tagId);
                var kindNameRaw = string.IsNullOrWhiteSpace(tag?.IconKind) ? "TagOutline" : tag!.IconKind;
                var kindName = NormalizeIconName(kindNameRaw);

                // Strongly-typed return to ensure MultiBinding -> Kind works reliably
                return Enum.TryParse<PackIconMaterialKind>(kindName, ignoreCase: true, out var parsed)
                    ? parsed
                    : PackIconMaterialKind.TagOutline;
            }
            return null;
        }
    }
}
