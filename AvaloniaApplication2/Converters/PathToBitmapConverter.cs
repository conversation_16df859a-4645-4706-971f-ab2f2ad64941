using System;
using System.Globalization;
using System.IO;
using Avalonia.Data.Converters;
using Avalonia.Media.Imaging;
using Avalonia.Platform;

namespace AvaloniaApplication2.Converters
{
    public class PathToBitmapConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string s && !string.IsNullOrWhiteSpace(s))
            {
                try
                {
                    // 1) Embedded Avalonia resources
                    if (s.StartsWith("avares://", StringComparison.OrdinalIgnoreCase) || s.StartsWith("/"))
                    {
                        // Support shorthand like "/Assets/Images/pills.png"
                        var uri = s.StartsWith("/")
                            ? new Uri($"avares://AvaloniaApplication2{s}")
                            : new Uri(s);

                        using var stream = AssetLoader.Open(uri);
                        return new Bitmap(stream);
                    }

                    // 2) File system path (absolute or relative)
                    var full = Path.GetFullPath(s);
                    if (File.Exists(full))
                    {
                        return new Bitmap(full);
                    }
                }
                catch
                {
                    // ignore bad images
                }
            }
            return null;
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotSupportedException();
        }
    }
}
