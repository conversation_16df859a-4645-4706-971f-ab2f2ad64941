using System;
using System.Collections.Generic;
using System.Globalization;
using Avalonia.Data.Converters;

namespace AvaloniaApplication2.Converters
{
    // Returns first non-empty path among inputs (<PERSON>splayPath, ImagePath)
    public class PreferDisplayPathConverter : IMultiValueConverter
    {
        public static readonly PreferDisplayPathConverter Instance = new PreferDisplayPathConverter();

        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            foreach (var v in values)
            {
                var s = v as string;
                if (!string.IsNullOrWhiteSpace(s))
                    return s;
            }
            return null;
        }
    }
}
