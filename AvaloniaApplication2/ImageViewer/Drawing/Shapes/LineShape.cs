using System;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public sealed class LineShape : ShapeBase
{
    private Point _start;
    private Point _end;

    public LineShape(Point start, Point end)
    {
        _start = start;
        _end = end;
        UpdateHandles();
    }

    public override ShapeKind Kind => ShapeKind.Line;

    public override Rect GetImageBounds()
    {
        var minX = Math.Min(_start.X, _end.X);
        var minY = Math.Min(_start.Y, _end.Y);
        var maxX = Math.Max(_start.X, _end.X);
        var maxY = Math.Max(_start.Y, _end.Y);
        return new Rect(minX, minY, Math.Max(1, maxX - minX), Math.Max(1, maxY - minY));
    }

    public override void Paint(DrawingContext ctx, Viewport vp)
    {
        var p0 = vp.ImageToView(_start);
        var p1 = vp.ImageToView(_end);

        // 计算缩放自适应的线条粗细
        double adaptiveThickness = CalculateAdaptiveThickness(BorderThick, vp.Scale);

        var pen = new Pen(BorderColor, adaptiveThickness)
        {
            LineCap = PenLineCap.Round,
            LineJoin = PenLineJoin.Round,
            MiterLimit = 2
        };
        ctx.DrawLine(pen, p0, p1);

        if (Selected)
        {
            foreach (var h in Handles)
            {
                h.Paint(ctx, vp);
            }
        }
    }

    public override void Move(Vector d)
    {
        // 计算当前边界并应用约束
        var currentBounds = GetImageBounds();
        var constrainedVector = ClampMoveVector(currentBounds, d);

        _start += constrainedVector;
        _end += constrainedVector;
        UpdateHandles();
    }

    public override void MoveHandle(int i, Point p)
    {
        // 应用边界约束到点位置
        var constrainedPoint = ClampPointToBounds(p);

        switch (i)
        {
            case 0: _start = constrainedPoint; break; // start
            case 1: _end = constrainedPoint; break;   // end
        }
        UpdateHandles();
    }

    public override HitResult HitTest(Point pt, double tol)
    {
        // handles first
        foreach (var h in Handles)
        {
            var dx = h.Position.X - pt.X;
            var dy = h.Position.Y - pt.Y;
            if ((dx * dx + dy * dy) <= tol * tol)
                return new HitResult(this, HitKind.Handle, h.Index);
        }

        // check distance to segment
        if (DistanceToSegment(pt, _start, _end) <= tol)
            return new HitResult(this, HitKind.Body, 0);

        return HitResult.None;
    }

    public override IShape DeepClone()
    {
        var s = new LineShape(_start, _end)
        {
            Selected = Selected,
            Fill = Fill,
            BorderColor = BorderColor,
            BorderThick = BorderThick
        };
        return s;
    }

    private static double DistanceToSegment(Point p, Point a, Point b)
    {
        var ab = b - a;
        var ab2 = ab.X * ab.X + ab.Y * ab.Y;
        if (ab2 <= 1e-6) return Math.Sqrt((p.X - a.X) * (p.X - a.X) + (p.Y - a.Y) * (p.Y - a.Y));
        var ap = p - a;
        var t = Math.Max(0, Math.Min(1, (ap.X * ab.X + ap.Y * ab.Y) / ab2));
        var proj = new Point(a.X + ab.X * t, a.Y + ab.Y * t);
        return Math.Sqrt((p.X - proj.X) * (p.X - proj.X) + (p.Y - proj.Y) * (p.Y - proj.Y));
    }

    private void UpdateHandles()
    {
        Handles.Clear();
        // endpoints as corner handles
        Handles.Add(new ShapeHandle(this, 0, _start, HandleRole.Corner, new Cursor(StandardCursorType.SizeAll)));
        Handles.Add(new ShapeHandle(this, 1, _end,   HandleRole.Corner, new Cursor(StandardCursorType.SizeAll)));
    }
}
