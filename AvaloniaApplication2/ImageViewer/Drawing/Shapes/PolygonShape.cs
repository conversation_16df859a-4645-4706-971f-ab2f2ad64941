using System;
using System.Collections.Generic;
using System.Linq;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public sealed class PolygonShape : ShapeBase
{
    public readonly List<Point> Points = new(); // image coords
    public bool Closed { get; set; } = true;
    public bool ShowHandles { get; set; } = true;

    public PolygonShape(IEnumerable<Point> pts, bool closed = true)
    {
        Points.AddRange(pts);
        Closed = closed;
        // create vertex handles, index from 1..N
        for (int i = 0; i < Points.Count; i++)
        {
            Handles.Add(new ShapeHandle(this, i + 1, Points[i], HandleRole.Corner, new Cursor(StandardCursorType.Arrow)));
        }
    }

    public override ShapeKind Kind => ShapeKind.Polygon;

    public override Rect GetImageBounds()
    {
        if (Points.Count == 0) return new Rect();
        double minX = Points.Min(p => p.X);
        double minY = Points.Min(p => p.Y);
        double maxX = Points.Max(p => p.X);
        double maxY = Points.Max(p => p.Y);
        return new Rect(new Point(minX, minY), new Point(maxX, maxY));
    }

    public override void Paint(DrawingContext ctx, Viewport vp)
    {
        if (Points.Count == 0) return;
        EnsureHandleSync();

        // 计算缩放自适应的线条粗细
        double adaptiveThickness = CalculateAdaptiveThickness(BorderThick, vp.Scale);

        var pen = new Pen(BorderColor, adaptiveThickness)
        {
            LineCap = PenLineCap.Round,
            LineJoin = PenLineJoin.Round,
            MiterLimit = 2
        };
        var viewPts = Points.Select(vp.ImageToView).ToList();
        if (Closed && viewPts.Count >= 3)
        {
            var geo = new StreamGeometry();
            using (var gc = geo.Open())
            {
                gc.BeginFigure(viewPts[0], Fill != null);
                for (int i = 1; i < viewPts.Count; i++) gc.LineTo(viewPts[i]);
                gc.EndFigure(true);
            }
            if (Fill != null) ctx.DrawGeometry(Fill, pen, geo);
            else ctx.DrawGeometry(null, pen, geo);
        }
        else
        {
            // draw as open polyline via geometry so joins/caps apply
            if (viewPts.Count >= 2)
            {
                var geo = new StreamGeometry();
                using (var gc = geo.Open())
                {
                    gc.BeginFigure(viewPts[0], false);
                    for (int i = 1; i < viewPts.Count; i++) gc.LineTo(viewPts[i]);
                    gc.EndFigure(false);
                }
                ctx.DrawGeometry(null, pen, geo);
            }
        }

        if (Selected && ShowHandles)
        {
            // update handles positions and paint
            var count = Math.Min(Points.Count, Handles.Count);
            for (int i = 0; i < count; i++)
            {
                Handles[i].Position = Points[i];
                Handles[i].Paint(ctx, vp);
            }
        }
    }

    public override HitResult HitTest(Point pt, double tol)
    {
        // handles first (optional)
        if (ShowHandles)
        {
            for (int i = 0; i < Points.Count; i++)
            {
                var dx = Points[i].X - pt.X;
                var dy = Points[i].Y - pt.Y;
                if (dx * dx + dy * dy <= tol * tol) return new HitResult(this, HitKind.Handle, i + 1);
            }
        }

        if (Closed && Points.Count >= 3)
        {
            // body hit (closed): point in polygon (even-odd)
            bool inside = false;
            for (int i = 0, j = Points.Count - 1; i < Points.Count; j = i++)
            {
                var pi = Points[i];
                var pj = Points[j];
                bool intersect = ((pi.Y > pt.Y) != (pj.Y > pt.Y)) &&
                                 (pt.X < (pj.X - pi.X) * (pt.Y - pi.Y) / ((pj.Y - pi.Y) == 0 ? 1e-9 : (pj.Y - pi.Y)) + pi.X);
                if (intersect) inside = !inside;
            }
            if (inside) return new HitResult(this, HitKind.Body, 0);
        }
        else if (!Closed && Points.Count >= 2)
        {
            // body hit (open polyline): distance to any segment <= tol
            for (int i = 1; i < Points.Count; i++)
            {
                if (DistancePointToSegmentSquared(pt, Points[i - 1], Points[i]) <= tol * tol)
                    return new HitResult(this, HitKind.Body, 0);
            }
        }
        return HitResult.None;
    }

    public override void Move(Vector d)
    {
        // 计算当前边界并应用约束
        var currentBounds = GetImageBounds();
        var constrainedVector = ClampMoveVector(currentBounds, d);

        for (int i = 0; i < Points.Count; i++)
        {
            Points[i] = Points[i] + constrainedVector;
        }
    }

    public override void MoveHandle(int i, Point p)
    {
        EnsureHandleSync();
        int idx = i - 1;
        if (idx >= 0 && idx < Points.Count)
        {
            // 应用边界约束到点位置
            Points[idx] = ClampPointToBounds(p);
        }
    }

    public override IShape DeepClone()
    {
        var copy = new PolygonShape(Points, Closed)
        {
            Selected = Selected,
            Fill = Fill,
            BorderColor = BorderColor,
            BorderThick = BorderThick
        };
        return copy;
    }

    private void EnsureHandleSync()
    {
        // grow
        for (int i = Handles.Count; i < Points.Count; i++)
        {
            Handles.Add(new ShapeHandle(this, i + 1, Points[i], HandleRole.Corner, new Cursor(StandardCursorType.Arrow)));
        }
        // shrink
        for (int i = Handles.Count - 1; i >= Points.Count; i--)
        {
            Handles.RemoveAt(i);
        }
    }

    private static double DistancePointToSegmentSquared(in Point p, in Point a, in Point b)
    {
        var abx = b.X - a.X; var aby = b.Y - a.Y;
        var apx = p.X - a.X; var apy = p.Y - a.Y;
        var ab2 = abx * abx + aby * aby;
        if (ab2 <= 1e-12) // a==b
        {
            var dx = apx; var dy = apy; return dx * dx + dy * dy;
        }
        var t = (apx * abx + apy * aby) / ab2;
        if (t < 0) t = 0; else if (t > 1) t = 1;
        var cx = a.X + t * abx; var cy = a.Y + t * aby;
        var dx2 = p.X - cx; var dy2 = p.Y - cy;
        return dx2 * dx2 + dy2 * dy2;
    }
}
