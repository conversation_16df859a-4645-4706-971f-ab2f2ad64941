using System;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public sealed class RectShape : ShapeBase
{
    public Rect Rect; // image coords
    public double AngleDeg; // 旋转角度（度），绕中心

    // 旋转句柄距离（DIP），类似ImageJ等专业图像处理软件的固定视觉距离
    private const double RotateHandleDistanceDip = 25.0; // 视图坐标下的固定距离（DIP）

    public RectShape(Rect r)
    {
        Rect = r;

        // 1,3,5,7 为四个角点；2,4,6,8 为四条边的中点；9 为旋转手柄（位于2号上方）
        Handles.Add(new ShapeHandle(this, 1,  Rect.TopLeft, HandleRole.Corner, new Cursor(StandardCursorType.TopLeftCorner)));
        Handles.Add(new ShapeHandle(this, 2,  new Point((Rect.Left+Rect.Right)/2, Rect.Top), HandleRole.Edge, new Cursor(StandardCursorType.SizeNorthSouth)));
        Handles.Add(new ShapeHandle(this, 3,  Rect.TopRight, HandleRole.Corner, new Cursor(StandardCursorType.TopRightCorner)));
        Handles.Add(new ShapeHandle(this, 4,  new Point(Rect.Right, (Rect.Top+Rect.Bottom)/2), HandleRole.Edge, new Cursor(StandardCursorType.SizeWestEast)));
        Handles.Add(new ShapeHandle(this, 5,  Rect.BottomRight, HandleRole.Corner, new Cursor(StandardCursorType.BottomRightCorner)));
        Handles.Add(new ShapeHandle(this, 6,  new Point((Rect.Left+Rect.Right)/2, Rect.Bottom), HandleRole.Edge, new Cursor(StandardCursorType.SizeNorthSouth)));
        Handles.Add(new ShapeHandle(this, 7,  Rect.BottomLeft, HandleRole.Corner, new Cursor(StandardCursorType.BottomLeftCorner)));
        Handles.Add(new ShapeHandle(this, 8,  new Point(Rect.Left, (Rect.Top+Rect.Bottom)/2), HandleRole.Edge, new Cursor(StandardCursorType.SizeWestEast)));
        // 旋转句柄位置将在Paint方法中动态计算，这里先设置一个临时位置
        Handles.Add(new ShapeHandle(this, 9,  new Point((Rect.Left+Rect.Right)/2, Rect.Top), HandleRole.Rotate, new Cursor(StandardCursorType.SizeAll)));
    }

    public override ShapeKind Kind => ShapeKind.Rect;
    public override Rect GetImageBounds() => Rect;

    public override void Paint(DrawingContext ctx, Viewport vp)
    {
        var center = Rect.Center;
        var viewCenter = vp.ImageToView(center);
        var radians = AngleDeg * Math.PI / 180.0;
        using (ctx.PushTransform(Matrix.CreateRotation(radians, viewCenter)))
        {
            var rv = vp.ImageToView(Rect);

            // 计算缩放自适应的线条粗细
            // 在高倍缩放时保持合理的视觉粗细，在低倍缩放时确保可见性
            double adaptiveThickness = CalculateAdaptiveThickness(BorderThick, vp.Scale);

            var pen = new Pen(BorderColor, adaptiveThickness)
            {
                LineJoin = PenLineJoin.Round,
                MiterLimit = 2
            };
            ctx.FillRectangle(Fill, rv);
            ctx.DrawRectangle(pen, rv);
        }
        if (Selected)
        {
            // 根据当前旋转角度更新句柄光标（用最接近的标准光标近似旋转效果）
            UpdateHandleCursors();

            // 更新9个手柄位置（考虑旋转和缩放）
            var handlePositions = GetCurrentHandlePositions(vp.Scale);

            for (int i = 0; i < Handles.Count && i < handlePositions.Length; i++)
            {
                Handles[i].Position = handlePositions[i];
                Handles[i].Paint(ctx, vp);
            }

            // 画一个小的旋转箭头（直接在视图坐标绘制）
            if (handlePositions.Length > 8) // 确保有旋转句柄
            {
                var rotHandle = handlePositions[8]; // 旋转句柄是第9个（索引8）
                var p0 = vp.ImageToView(rotHandle);

                // 自适应的箭头大小，类似ImageJ
                var radius = Math.Max(8, Math.Min(15, 10 * Math.Sqrt(vp.Scale)));
                var start = new Point(p0.X - radius * 0.6, p0.Y);
                var end   = new Point(p0.X + radius * 0.6, p0.Y);
                var geo = new StreamGeometry();
                using (var gc = geo.Open())
                {
                    gc.BeginFigure(start, false);
                    gc.QuadraticBezierTo(new Point(p0.X, p0.Y - radius * 0.6), end);
                    gc.EndFigure(false);
                }

                // 自适应的线条粗细
                var arrowThickness = Math.Max(1.0, Math.Min(2.0, vp.Scale * 0.5));
                ctx.DrawGeometry(null, new Pen(BorderColor, arrowThickness), geo);

                // 箭头头部
                var ah = Math.Max(3, Math.Min(6, radius * 0.4));
                ctx.DrawLine(new Pen(BorderColor, arrowThickness), end, new Point(end.X - ah, end.Y - ah));
                ctx.DrawLine(new Pen(BorderColor, arrowThickness), end, new Point(end.X - ah, end.Y + ah));
            }
        }
    }

    public override HitResult HitTest(Point pt, double tol)
    {
        // 先检测句柄（需要使用当前的句柄位置）
        var handlePositions = GetCurrentHandlePositions(1.0); // 使用默认缩放进行HitTest

        for (int i = 0; i < handlePositions.Length; i++)
        {
            var dx = handlePositions[i].X - pt.X;
            var dy = handlePositions[i].Y - pt.Y;
            if ((dx * dx + dy * dy) <= tol * tol)
            {
                return new HitResult(this, HitKind.Handle, i + 1);
            }
        }

        // 将点逆旋转回未旋转空间以做矩形包含测试
        var inv = RotatePoint(pt, Rect.Center, -AngleDeg);
        if (Rect.Contains(inv))
        {
            return new HitResult(this, HitKind.Body, 0);
        }
        return HitResult.None;
    }

    /// <summary>
    /// 获取当前的句柄位置数组
    /// </summary>
    /// <param name="scale">当前缩放比例</param>
    /// <returns>句柄位置数组</returns>
    private Point[] GetCurrentHandlePositions(double scale)
    {
        var c = Rect.Center;
        var tl = RotatePoint(Rect.TopLeft, c, AngleDeg);
        var tc = RotatePoint(new Point((Rect.Left+Rect.Right)/2, Rect.Top), c, AngleDeg);
        var tr = RotatePoint(Rect.TopRight, c, AngleDeg);
        var rc = RotatePoint(new Point(Rect.Right, (Rect.Top+Rect.Bottom)/2), c, AngleDeg);
        var br = RotatePoint(Rect.BottomRight, c, AngleDeg);
        var bc = RotatePoint(new Point((Rect.Left+Rect.Right)/2, Rect.Bottom), c, AngleDeg);
        var bl = RotatePoint(Rect.BottomLeft, c, AngleDeg);
        var lc = RotatePoint(new Point(Rect.Left, (Rect.Top+Rect.Bottom)/2), c, AngleDeg);

        // 计算旋转句柄位置：基于视图坐标的固定距离
        var dirUp = Normalize(tc - c);
        var rotHandleDistanceImage = RotateHandleDistanceDip / scale;
        var rotHandle = tc + dirUp * rotHandleDistanceImage;

        return new Point[] { tl, tc, tr, rc, br, bc, bl, lc, rotHandle };
    }

    public override void Move(Vector d) => Rect = new Rect(Rect.Position + d, Rect.Size);

    public override void MoveHandle(int i, Point p)
    {
        // 懒初始化拖拽会话：当按下某个句柄开始拖拽时，记录初始矩形与中心，作为所有计算的参考
        if (_dragHandleIndex == 0 || _dragHandleIndex != i)
        {
            _dragHandleIndex = i;
            _dragRect0 = Rect;
            _dragCenter0 = Rect.Center;
            _angle0 = AngleDeg;
        }
        if (i == 9) // rotate handle
        {
            var c = Rect.Center;
            var v = p - c;
            var angle = Math.Atan2(v.Y, v.X) * 180.0 / Math.PI + 90.0; // 使向上为0°
            AngleDeg = angle;
            return;
        }

        // 尺寸编辑：把点逆旋转到未旋转空间，按轴对齐矩形编辑
        var pLocal = RotatePoint(p, _dragCenter0, -_angle0);
        var r = _dragRect0;
        double left = r.Left, top = r.Top, right = r.Right, bottom = r.Bottom;
        // 记录当前拖拽的“锚定类型”，用于保持世界坐标下的锚点（角/边）不漂移
        // 注意：对于边句柄，发生越界翻面后，固定的“逻辑锚边”在 newRect 中对应的几何边会互换
        // 因此需要在计算完成后做一次锚边重映射，以允许翻面继续
        AnchorType anchorType = AnchorType.None;
        AnchorType? anchorTypeEffective = null; // 仅对边句柄在越界时重映射
        switch (i)
        {
            // 角点：对角点保持不动
            case 1: // TopLeft
            {
                var anchor = r.BottomRight;
                left = pLocal.X; top = pLocal.Y; right = anchor.X; bottom = anchor.Y;
                anchorType = AnchorType.Corner_BR;
                break;
            }
            case 3: // TopRight
            {
                var anchor = r.BottomLeft;
                top = pLocal.Y; right = pLocal.X; left = anchor.X; bottom = anchor.Y;
                anchorType = AnchorType.Corner_BL;
                break;
            }
            case 5: // BottomRight
            {
                var anchor = r.TopLeft;
                right = pLocal.X; bottom = pLocal.Y; left = anchor.X; top = anchor.Y;
                anchorType = AnchorType.Corner_TL;
                break;
            }
            case 7: // BottomLeft
            {
                var anchor = r.TopRight;
                left = pLocal.X; bottom = pLocal.Y; right = anchor.X; top = anchor.Y;
                anchorType = AnchorType.Corner_TR;
                break;
            }
            // 边中点：对应边保持不动；越过时交换拖拽边与锚边以实现“翻面继续拖”手感
            case 2: // TopCenter（锚：Bottom）
            {
                var anchor = r.Bottom;
                bool crossed = pLocal.Y > anchor;
                if (!crossed) { top = pLocal.Y; bottom = anchor; }
                else { top = anchor; bottom = pLocal.Y; /* 反向后鼠标成为 Bottom 边 */ }
                anchorType = AnchorType.Edge_Bottom; // 逻辑锚：Bottom
                anchorTypeEffective = crossed ? AnchorType.Edge_Top : AnchorType.Edge_Bottom;
                break;
            }
            case 6: // BottomCenter（锚：Top）
            {
                var anchor = r.Top;
                bool crossed = pLocal.Y < anchor;
                if (!crossed) { top = anchor; bottom = pLocal.Y; }
                else { top = pLocal.Y; bottom = anchor; /* 反向后鼠标成为 Top 边 */ }
                anchorType = AnchorType.Edge_Top; // 逻辑锚：Top
                anchorTypeEffective = crossed ? AnchorType.Edge_Bottom : AnchorType.Edge_Top;
                break;
            }
            case 4: // RightCenter（锚：Left）
            {
                var anchor = r.Left;
                bool crossed = pLocal.X < anchor;
                if (!crossed) { left = anchor; right = pLocal.X; }
                else { left = pLocal.X; right = anchor; /* 反向后鼠标成为 Left 边 */ }
                anchorType = AnchorType.Edge_Left; // 逻辑锚：Left
                anchorTypeEffective = crossed ? AnchorType.Edge_Right : AnchorType.Edge_Left;
                break;
            }
            case 8: // LeftCenter（锚：Right）
            {
                var anchor = r.Right;
                bool crossed = pLocal.X > anchor;
                if (!crossed) { left = pLocal.X; right = anchor; }
                else { left = anchor; right = pLocal.X; /* 反向后鼠标成为 Right 边 */ }
                anchorType = AnchorType.Edge_Right; // 逻辑锚：Right
                anchorTypeEffective = crossed ? AnchorType.Edge_Left : AnchorType.Edge_Right;
                break;
            }
        }
        // 允许越界翻面：归一化
        var nl = Math.Min(left, right);
        var nr = Math.Max(left, right);
        var nt = Math.Min(top, bottom);
        var nb = Math.Max(top, bottom);
        var newRect = new Rect(new Point(nl, nt), new Point(nr, nb));

        // 保持锚点在世界坐标下不漂移：对齐拖拽开始时缓存的 _anchorWorld
        if (_dragging && anchorType != AnchorType.None)
        {
            var eff = anchorTypeEffective ?? anchorType;
            var newAnchorWorld = GetAnchorWorld(newRect, _angle0, eff);
            var delta = _anchorWorld - newAnchorWorld; // 图像坐标下平移向量
            newRect = new Rect(newRect.Position + delta, newRect.Size);
        }

        Rect = newRect;
    }
    
    public override IShape DeepClone() => new RectShape(Rect){ Selected=Selected, Fill=Fill, BorderColor=BorderColor, BorderThick=BorderThick, AngleDeg=AngleDeg };

    private static Point RotatePoint(Point p, Point center, double angleDeg)
    {
        var rad = angleDeg * Math.PI / 180.0;
        var s = Math.Sin(rad);
        var c = Math.Cos(rad);
        var dx = p.X - center.X;
        var dy = p.Y - center.Y;
        var x = dx * c - dy * s;
        var y = dx * s + dy * c;
        return new Point(center.X + x, center.Y + y);
    }

    private static Vector Normalize(Vector v)
    {
        var len = Math.Sqrt(v.X * v.X + v.Y * v.Y);
        if (len < 1e-6) return new Vector(0, -1); // 默认向上
        return new Vector(v.X / len, v.Y / len);
    }

    private void UpdateHandleCursors()
    {
        // 角度归一化到 [0,180)
        double a = AngleDeg % 180.0; if (a < 0) a += 180.0;

        // 边：当接近90°时，NS/WE 互换，以近似“随旋转”的视觉
        bool swapEdge = (a >= 45 && a < 135);

        foreach (var h in Handles)
        {
            switch (h.Index)
            {
                case 2: // Top
                case 6: // Bottom
                    h.Cursor = new Cursor(swapEdge ? StandardCursorType.SizeWestEast : StandardCursorType.SizeNorthSouth);
                    break;
                case 4: // Right
                case 8: // Left
                    h.Cursor = new Cursor(swapEdge ? StandardCursorType.SizeNorthSouth : StandardCursorType.SizeWestEast);
                    break;
                case 1: // TL 角
                case 5: // BR 角（与 TL 同对角线组）
                    h.Cursor = new Cursor((a >= 45 && a < 135) ? StandardCursorType.TopRightCorner : StandardCursorType.TopLeftCorner);
                    break;
                case 3: // TR 角
                case 7: // BL 角（与 TR 同对角线组）
                    h.Cursor = new Cursor((a >= 45 && a < 135) ? StandardCursorType.TopLeftCorner : StandardCursorType.TopRightCorner);
                    break;
                case 9:
                    h.Cursor = new Cursor(StandardCursorType.SizeAll); // 若有自定义旋转光标可替换
                    break;
            }
        }
    }

    // 拖拽上下文（用于旋转状态下保持锚边/中心稳定）
    private int _dragHandleIndex;
    private Rect _dragRect0;
    private Point _dragCenter0;
    private double _angle0;
    private bool _dragging;
    private Point _anchorWorld;

    public void BeginHandleDrag(int index)
    {
        _dragHandleIndex = index;
        _dragRect0 = Rect;
        _dragCenter0 = Rect.Center;
        _angle0 = AngleDeg;
        _dragging = true;
        _anchorWorld = GetAnchorWorld(_dragRect0, _angle0, AnchorFromHandle(index));
    }
    public void EndHandleDrag()
    {
        _dragHandleIndex = 0;
        _dragging = false;
    }

    private enum AnchorType { None, Corner_TL, Corner_TR, Corner_BL, Corner_BR, Edge_Left, Edge_Right, Edge_Top, Edge_Bottom }

    private static AnchorType AnchorFromHandle(int handle)
    {
        return handle switch
        {
            1 => AnchorType.Corner_BR,
            3 => AnchorType.Corner_BL,
            5 => AnchorType.Corner_TL,
            7 => AnchorType.Corner_TR,
            2 => AnchorType.Edge_Bottom,
            6 => AnchorType.Edge_Top,
            4 => AnchorType.Edge_Left,
            8 => AnchorType.Edge_Right,
            _ => AnchorType.None
        };
    }

    private static Point GetAnchorWorld(Rect rect, double angleDeg, AnchorType t)
    {
        var c = rect.Center;
        Point local = t switch
        {
            AnchorType.Corner_TL => rect.TopLeft,
            AnchorType.Corner_TR => rect.TopRight,
            AnchorType.Corner_BL => rect.BottomLeft,
            AnchorType.Corner_BR => rect.BottomRight,
            AnchorType.Edge_Left => new Point(rect.Left, (rect.Top + rect.Bottom) / 2),
            AnchorType.Edge_Right => new Point(rect.Right, (rect.Top + rect.Bottom) / 2),
            AnchorType.Edge_Top => new Point((rect.Left + rect.Right) / 2, rect.Top),
            AnchorType.Edge_Bottom => new Point((rect.Left + rect.Right) / 2, rect.Bottom),
            _ => rect.Center
        };
        return RotatePoint(local, c, angleDeg);
    }
}
