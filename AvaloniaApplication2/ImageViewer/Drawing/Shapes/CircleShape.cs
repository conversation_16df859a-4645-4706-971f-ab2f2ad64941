using System;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;

namespace ImageViewer.Drawing;

// Ellipse based on a bounding rectangle with 4 mid-side handles (N/E/S/W)
public sealed class CircleShape : ShapeBase
{
    private Rect _rect; // image coordinates

    public CircleShape(Point center, double radius)
    {
        // Backward-compat: initialize as a square bounds
        var r = Math.Max(1, radius);
        _rect = new Rect(center.X - r, center.Y - r, r * 2, r * 2);
        UpdateHandles();
    }

    // New creation via bounds
    public CircleShape(Rect bounds)
    {
        _rect = Normalize(bounds);
        UpdateHandles();
    }

    public void SetBounds(Rect bounds)
    {
        _rect = Normalize(bounds);
        UpdateHandles();
    }

    public override ShapeKind Kind => ShapeKind.Circle;

    public override Rect GetImageBounds() => _rect;

    public override void Paint(DrawingContext ctx, Viewport vp)
    {
        var rv = vp.ImageToView(_rect);

        // 计算缩放自适应的线条粗细
        double adaptiveThickness = CalculateAdaptiveThickness(BorderThick, vp.Scale);

        var pen = new Pen(BorderColor, adaptiveThickness)
        {
            LineJoin = PenLineJoin.Round,
            MiterLimit = 2
        };
        ctx.DrawEllipse(Fill, pen, rv);

        if (Selected)
        {
            foreach (var h in Handles)
                h.Paint(ctx, vp);
        }
    }

    public override void Move(Vector d)
    {
        _rect = new Rect(_rect.Position + d, _rect.Size);
        UpdateHandles();
    }

    public override void MoveHandle(int i, Point p)
    {
        // Handles: 0=N,1=E,2=S,3=W
        var x = _rect.X; var y = _rect.Y; var w = _rect.Width; var h = _rect.Height;
        switch (i)
        {
            case 0: // N
                h = (y + h) - p.Y; y = p.Y; break;
            case 1: // E
                w = p.X - x; break;
            case 2: // S
                h = p.Y - y; break;
            case 3: // W
                w = (x + w) - p.X; x = p.X; break;
            default:
                return;
        }
        _rect = Normalize(new Rect(x, y, w, h));
        UpdateHandles();
    }

    public override HitResult HitTest(Point pt, double tol)
    {
        // handles
        foreach (var h in Handles)
        {
            var dx = h.Position.X - pt.X;
            var dy = h.Position.Y - pt.Y;
            if ((dx * dx + dy * dy) <= tol * tol)
                return new HitResult(this, HitKind.Handle, h.Index);
        }

        // body: point-in-ellipse test
        var cx = _rect.X + _rect.Width / 2.0;
        var cy = _rect.Y + _rect.Height / 2.0;
        var rx = Math.Max(1e-3, _rect.Width / 2.0);
        var ry = Math.Max(1e-3, _rect.Height / 2.0);
        var nx = (pt.X - cx) / rx;
        var ny = (pt.Y - cy) / ry;
        if (nx * nx + ny * ny <= 1.0)
            return new HitResult(this, HitKind.Body, 0);

        return HitResult.None;
    }

    public override IShape DeepClone()
    {
        var s = new CircleShape(_rect)
        {
            Selected = Selected,
            Fill = Fill,
            BorderColor = BorderColor,
            BorderThick = BorderThick
        };
        return s;
    }

    private void UpdateHandles()
    {
        Handles.Clear();
        var cx = _rect.X + _rect.Width / 2.0;
        var cy = _rect.Y + _rect.Height / 2.0;
        // N, E, S, W midpoint handles
        Handles.Add(new ShapeHandle(this, 0, new Point(cx, _rect.Y),                 HandleRole.Edge, new Cursor(StandardCursorType.TopSide)));
        Handles.Add(new ShapeHandle(this, 1, new Point(_rect.X + _rect.Width, cy),  HandleRole.Edge, new Cursor(StandardCursorType.RightSide)));
        Handles.Add(new ShapeHandle(this, 2, new Point(cx, _rect.Y + _rect.Height), HandleRole.Edge, new Cursor(StandardCursorType.BottomSide)));
        Handles.Add(new ShapeHandle(this, 3, new Point(_rect.X, cy),                 HandleRole.Edge, new Cursor(StandardCursorType.LeftSide)));
    }

    private static Rect Normalize(Rect r)
    {
        if (r.Width < 0) { r = new Rect(r.X + r.Width, r.Y, -r.Width, r.Height); }
        if (r.Height < 0) { r = new Rect(r.X, r.Y + r.Height, r.Width, -r.Height); }
        return r;
    }
}
