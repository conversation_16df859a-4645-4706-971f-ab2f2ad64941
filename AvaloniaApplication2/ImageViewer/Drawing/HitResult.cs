using Avalonia;

namespace ImageViewer.Drawing;

public readonly struct HitResult
{
    public static readonly HitResult None = new(null!, HitKind.None, 0);

    public IShape? Target { get; }
    public HitKind Kind { get; }
    public int HandleIndex { get; }

    public HitResult(IShape? target, HitKind kind, int handleIndex)
    {
        Target = target;
        Kind = kind;
        HandleIndex = handleIndex;
    }
}
