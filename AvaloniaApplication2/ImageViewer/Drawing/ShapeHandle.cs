using Avalonia;
using Avalonia.Input;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public sealed class ShapeHandle
{
    public readonly IShape Owner;
    public readonly int Index;
    public Point Position;
    public HandleRole Role;
    public Cursor Cursor;

    public ShapeHandle(IShape owner, int index, Point pos, HandleRole role, Cursor? cursor = null)
    {
        Owner = owner;
        Index = index;
        Position = pos;
        Role = role;
        Cursor = cursor ?? new Cursor(StandardCursorType.Arrow);
    }

    public void Paint(DrawingContext ctx, Viewport vp)
    {
        // 句柄渲染使用固定的DIP尺寸，不受图像缩放影响
        var p = vp.ImageToView(Position);

        // 固定的句柄大小（DIP），类似专业图像处理软件
        const double HandleSizeDip = 4.0;

        var rect = new Rect(p.X - HandleSizeDip, p.Y - HandleSizeDip,
                           HandleSizeDip * 2, HandleSizeDip * 2);

        // 使用专业的句柄样式：蓝色填充，黑色边框
        var fill = Brushes.DeepSkyBlue;
        var pen = new Pen(Brushes.Black, 1.0);

        ctx.DrawEllipse(fill, pen, rect);
    }
}
