using System;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public sealed class ShapeHandle
{
    public readonly IShape Owner;
    public readonly int Index;
    public Point Position;
    public HandleRole Role;
    public Cursor Cursor;

    public ShapeHandle(IShape owner, int index, Point pos, HandleRole role, Cursor? cursor = null)
    {
        Owner = owner;
        Index = index;
        Position = pos;
        Role = role;
        Cursor = cursor ?? new Cursor(StandardCursorType.Arrow);
    }

    public void Paint(DrawingContext ctx, Viewport vp)
    {
        // 句柄渲染使用自适应的DIP尺寸，类似ImageJ的行为
        var p = vp.ImageToView(Position);

        // 计算自适应的句柄大小，类似ImageJ等专业图像处理软件
        double handleSize = CalculateAdaptiveHandleSize(vp.Scale);

        var rect = new Rect(p.X - handleSize, p.Y - handleSize,
                           handleSize * 2, handleSize * 2);

        // 使用专业的句柄样式：蓝色填充，黑色边框
        var fill = Brushes.DeepSkyBlue;
        var pen = new Pen(Brushes.Black, 1.0);

        ctx.DrawEllipse(fill, pen, rect);
    }

    /// <summary>
    /// 计算自适应的句柄大小，类似ImageJ的行为
    /// </summary>
    /// <param name="scale">当前缩放比例</param>
    /// <returns>句柄大小（DIP）</returns>
    private static double CalculateAdaptiveHandleSize(double scale)
    {
        // ImageJ风格的句柄大小策略：
        // 1. 在低倍缩放时，保持较大的句柄便于操作
        // 2. 在高倍缩放时，使用较小的句柄提高精度

        const double MinHandleSize = 3.0;    // 最小句柄大小（DIP）
        const double MaxHandleSize = 6.0;    // 最大句柄大小（DIP）

        if (scale < 0.5)
        {
            // 低倍缩放：使用较大句柄
            return MaxHandleSize;
        }
        else if (scale > 4.0)
        {
            // 高倍缩放：使用较小句柄
            return MinHandleSize;
        }
        else
        {
            // 中等缩放：线性插值
            double t = Math.Log(scale + 0.5) / Math.Log(4.5); // 对数插值
            return MaxHandleSize - t * (MaxHandleSize - MinHandleSize);
        }
    }
}
