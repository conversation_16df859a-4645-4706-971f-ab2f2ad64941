using Avalonia;

namespace ImageViewer.Drawing;

/// <summary>
/// 封装 图像坐标 - 视图坐标 的线性变换。
/// 图像坐标：左上角为原点，向右为X轴正方向，向下为Y轴正方向。
/// 视图坐标：左上角为原点，向右为X轴正方向，向下为Y轴正方向。
/// </summary>
public readonly struct Viewport
{
    public readonly double Scale;      // image->view 缩放
    public readonly Point Offset;      // image->view 平移（DIP）

    public Viewport(double scale, Point offset)
    {
        Scale = scale;
        Offset = offset;
    }
    public Point ImageToView(Point p) => new(Offset.X + p.X * Scale, Offset.Y + p.Y * Scale);
    public Rect  ImageToView(Rect r)  => new(Offset.X + r.X * Scale, Offset.Y + r.Y * Scale,
                                         r.Width * Scale, r.Height * Scale);
    public Point ViewToImage(Point v) => new((v.X - Offset.X) / Scale, (v.Y - Offset.Y) / Scale);
}
