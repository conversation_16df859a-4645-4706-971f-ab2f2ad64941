using System;
using System.Collections.Generic;
using Avalonia;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public abstract class ShapeBase : IShape
{
    public bool Selected { get; set; }
    public IBrush? Fill { get; set; }
    public IBrush BorderColor { get; set; } = Brushes.Lime;
    public double BorderThick { get; set; } = 1.0;

    public readonly List<ShapeHandle> Handles = new();

    // 图像边界约束（图像坐标）
    protected static Rect? _imageBounds = null;

    /// <summary>
    /// 设置全局图像边界，用于约束形状绘制
    /// </summary>
    /// <param name="bounds">图像边界（图像坐标）</param>
    public static void SetImageBounds(Rect bounds)
    {
        _imageBounds = bounds;
    }

    public abstract ShapeKind Kind { get; }
    public abstract Rect GetImageBounds();
    public virtual Rect GetViewBounds(Viewport vp) => vp.ImageToView(GetImageBounds());
    public abstract void Paint(DrawingContext ctx, Viewport vp);
    public abstract HitResult HitTest(Avalonia.Point pt, double tol);
    public abstract void Move(Avalonia.Vector d);
    public abstract void MoveHandle(int i, Avalonia.Point p);
    public abstract IShape DeepClone();

    /// <summary>
    /// 计算缩放自适应的线条粗细，类似ImageJ等专业图像处理软件的行为
    /// </summary>
    /// <param name="originalThickness">原始线条粗细（图像坐标）</param>
    /// <param name="scale">当前缩放比例</param>
    /// <returns>自适应的线条粗细（视图坐标）</returns>
    protected static double CalculateAdaptiveThickness(double originalThickness, double scale)
    {
        if (originalThickness <= 0) originalThickness = 1.0;

        // ImageJ风格的线条粗细策略：
        // 1. 在极小缩放时（< 0.25），使用固定的最小粗细确保可见性
        // 2. 在小缩放时（0.25 - 1.0），线性插值到标准粗细
        // 3. 在正常缩放时（1.0 - 2.0），按比例缩放
        // 4. 在高倍缩放时（> 2.0），使用对数缩放避免过粗

        const double MinThickness = 1.0;      // 最小可见粗细（DIP）
        const double StandardThickness = 1.5; // 标准粗细（DIP）
        const double MaxThickness = 2.5;      // 最大粗细（DIP）
        const double SmallScaleThreshold = 0.25;  // 小缩放阈值
        const double NormalScaleThreshold = 1.0;  // 正常缩放阈值
        const double HighScaleThreshold = 2.0;    // 高倍缩放阈值

        if (scale < SmallScaleThreshold)
        {
            // 极小缩放：固定最小粗细
            return MinThickness;
        }
        else if (scale < NormalScaleThreshold)
        {
            // 小缩放：线性插值
            double t = (scale - SmallScaleThreshold) / (NormalScaleThreshold - SmallScaleThreshold);
            return MinThickness + t * (StandardThickness - MinThickness);
        }
        else if (scale <= HighScaleThreshold)
        {
            // 正常缩放：按比例缩放
            return StandardThickness * scale;
        }
        else
        {
            // 高倍缩放：对数缩放，类似ImageJ的行为
            double logScale = Math.Log(scale / HighScaleThreshold) / Math.Log(2.0) + 1.0;
            double thickness = StandardThickness * HighScaleThreshold * Math.Pow(logScale, 0.5);
            return Math.Clamp(thickness, StandardThickness, MaxThickness);
        }
    }

    /// <summary>
    /// 将点约束到图像边界内
    /// </summary>
    /// <param name="point">原始点</param>
    /// <returns>约束后的点</returns>
    public static Point ClampPointToBounds(Point point)
    {
        if (_imageBounds == null) return point;

        var bounds = _imageBounds.Value;
        return new Point(
            Math.Clamp(point.X, bounds.Left, bounds.Right),
            Math.Clamp(point.Y, bounds.Top, bounds.Bottom)
        );
    }

    /// <summary>
    /// 将矩形约束到图像边界内
    /// </summary>
    /// <param name="rect">原始矩形</param>
    /// <returns>约束后的矩形</returns>
    public static Rect ClampRectToBounds(Rect rect)
    {
        if (_imageBounds == null) return rect;

        var bounds = _imageBounds.Value;

        // 确保矩形不超出边界
        var left = Math.Max(bounds.Left, rect.Left);
        var top = Math.Max(bounds.Top, rect.Top);
        var right = Math.Min(bounds.Right, rect.Right);
        var bottom = Math.Min(bounds.Bottom, rect.Bottom);

        // 确保矩形有效（宽度和高度为正）
        if (right <= left || bottom <= top)
        {
            // 如果约束后矩形无效，返回一个最小的有效矩形
            var centerX = Math.Clamp(rect.Center.X, bounds.Left, bounds.Right);
            var centerY = Math.Clamp(rect.Center.Y, bounds.Top, bounds.Bottom);
            const double minSize = 1.0;

            left = Math.Max(bounds.Left, centerX - minSize / 2);
            top = Math.Max(bounds.Top, centerY - minSize / 2);
            right = Math.Min(bounds.Right, centerX + minSize / 2);
            bottom = Math.Min(bounds.Bottom, centerY + minSize / 2);
        }

        return new Rect(left, top, right - left, bottom - top);
    }

    /// <summary>
    /// 将向量约束，确保移动后的形状不超出边界
    /// </summary>
    /// <param name="currentBounds">当前形状边界</param>
    /// <param name="moveVector">移动向量</param>
    /// <returns>约束后的移动向量</returns>
    protected static Vector ClampMoveVector(Rect currentBounds, Vector moveVector)
    {
        if (_imageBounds == null) return moveVector;

        var bounds = _imageBounds.Value;
        var newBounds = new Rect(currentBounds.Position + moveVector, currentBounds.Size);

        var deltaX = moveVector.X;
        var deltaY = moveVector.Y;

        // 约束X方向移动
        if (newBounds.Left < bounds.Left)
            deltaX = bounds.Left - currentBounds.Left;
        else if (newBounds.Right > bounds.Right)
            deltaX = bounds.Right - currentBounds.Right;

        // 约束Y方向移动
        if (newBounds.Top < bounds.Top)
            deltaY = bounds.Top - currentBounds.Top;
        else if (newBounds.Bottom > bounds.Bottom)
            deltaY = bounds.Bottom - currentBounds.Bottom;

        return new Vector(deltaX, deltaY);
    }
}
