using System.Collections.Generic;
using Avalonia;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public abstract class ShapeBase : IShape
{
    public bool Selected { get; set; }
    public IBrush? Fill { get; set; }
    public IBrush BorderColor { get; set; } = Brushes.Lime;
    public double BorderThick { get; set; } = 1.0;

    public readonly List<ShapeHandle> Handles = new();

    public abstract ShapeKind Kind { get; }
    public abstract Rect GetImageBounds();
    public virtual Rect GetViewBounds(Viewport vp) => vp.ImageToView(GetImageBounds());
    public abstract void Paint(DrawingContext ctx, Viewport vp);
    public abstract HitResult HitTest(Avalonia.Point pt, double tol);
    public abstract void Move(Avalonia.Vector d);
    public abstract void MoveHandle(int i, Avalonia.Point p);
    public abstract IShape DeepClone();

    /// <summary>
    /// 计算缩放自适应的线条粗细，类似专业图像处理软件的行为
    /// </summary>
    /// <param name="originalThickness">原始线条粗细（图像坐标）</param>
    /// <param name="scale">当前缩放比例</param>
    /// <returns>自适应的线条粗细（视图坐标）</returns>
    protected static double CalculateAdaptiveThickness(double originalThickness, double scale)
    {
        if (originalThickness <= 0) originalThickness = 1.0;

        // 专业图像处理软件的线条粗细策略：
        // 1. 在低倍缩放时（< 1.0），保持最小可见粗细
        // 2. 在正常缩放时（1.0 - 4.0），按比例缩放
        // 3. 在高倍缩放时（> 4.0），限制最大粗细避免过粗

        const double MinThickness = 1.0;  // 最小可见粗细（DIP）
        const double MaxThickness = 3.0;  // 最大粗细（DIP）
        const double ScaleThreshold = 4.0; // 开始限制粗细的缩放阈值

        double scaledThickness = originalThickness * scale;

        if (scale < 1.0)
        {
            // 缩小时：确保线条仍然可见
            return Math.Max(MinThickness, scaledThickness);
        }
        else if (scale <= ScaleThreshold)
        {
            // 正常缩放：按比例缩放，但不小于最小粗细
            return Math.Max(MinThickness, scaledThickness);
        }
        else
        {
            // 高倍缩放：限制最大粗细，避免线条过粗影响精度
            double factor = ScaleThreshold / scale;
            double limitedThickness = originalThickness * ScaleThreshold * factor;
            return Math.Clamp(limitedThickness, MinThickness, MaxThickness);
        }
    }
}
