using System;
using System.Collections.Generic;
using Avalonia;
using Avalonia.Media;

namespace ImageViewer.Drawing;

public interface IShape
{
    ShapeKind Kind { get; }
    bool Selected { get; set; }
    IBrush? Fill { get; set; }
    IBrush BorderColor { get; set; }
    double BorderThick { get; set; }

    Rect GetImageBounds();
    Rect GetViewBounds(Viewport vp);
    void Paint(DrawingContext ctx, Viewport vp);
    HitResult HitTest(Point pt, double tol);
    void Move(Vector d);
    void MoveHandle(int i, Point p);
    IShape DeepClone();
}
