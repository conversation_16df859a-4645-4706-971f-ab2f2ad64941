using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Globalization;
using System.Runtime.InteropServices;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using Avalonia.Media.Immutable;
using Avalonia.Media.TextFormatting;
using ImageViewer.Drawing;

namespace ImageViewer.Controls;

public sealed class RenderView : Control
{
    public static readonly StyledProperty<Bitmap?> SourceProperty =
        AvaloniaProperty.Register<RenderView, Bitmap?>(nameof(Source));

    public static readonly StyledProperty<ToolMode> ToolProperty =
        AvaloniaProperty.Register<RenderView, ToolMode>(nameof(Tool), defaultValue: ToolMode.Edit);

    public static readonly StyledProperty<double> ZoomProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(Zoom), 1.0);

    public static readonly StyledProperty<IBrush> StrokeProperty =
        AvaloniaProperty.Register<RenderView, IBrush>(nameof(Stroke), Brushes.Red);

    public static readonly StyledProperty<double> StrokeThicknessProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(StrokeThickness), 1.0);

    public static readonly StyledProperty<bool> ShowPixelGridProperty =
        AvaloniaProperty.Register<RenderView, bool>(nameof(ShowPixelGrid), true);
    public bool ShowPixelGrid
    {
        get => GetValue(ShowPixelGridProperty);
        set => SetValue(ShowPixelGridProperty, value);
    }

    public static readonly StyledProperty<IBrush> GridLineBrushProperty =
        AvaloniaProperty.Register<RenderView, IBrush>(nameof(GridLineBrush),
            new SolidColorBrush(Color.FromArgb(0x96, 0x64, 0x64, 0x64)));
    public IBrush GridLineBrush
    {
        get => GetValue(GridLineBrushProperty);
        set => SetValue(GridLineBrushProperty, value);
    }

    // 以"设备像素"为单位配置线宽，自动换算到 DIP，避免高 DPI 下过粗/过细
    public static readonly StyledProperty<double> GridLineThicknessInDevicePixelsProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(GridLineThicknessInDevicePixels), 1.0);
    public double GridLineThicknessInDevicePixels
    {
        get => GetValue(GridLineThicknessInDevicePixelsProperty);
        set => SetValue(GridLineThicknessInDevicePixelsProperty, value);
    }

    public Bitmap? Source
    {
        get => GetValue(SourceProperty);
        set => SetValue(SourceProperty, value);
    }

    // Public helper for external callers (e.g., ImageView) to convert from control-space to image-space
    public Point MapViewToImage(Point viewPoint)
    {
        return ViewToImage(viewPoint);
    }

    public ToolMode Tool
    {
        get => GetValue(ToolProperty);
        set => SetValue(ToolProperty, value);
    }

    public double Zoom
    {
        get => GetValue(ZoomProperty);
        set => SetValue(ZoomProperty, value);
    }

    public IBrush Stroke
    {
        get => GetValue(StrokeProperty);
        set => SetValue(StrokeProperty, value);
    }

    public double StrokeThickness
    {
        get => GetValue(StrokeThicknessProperty);
        set => SetValue(StrokeThicknessProperty, value);
    }

    public ObservableCollection<IShape> Shapes { get; } = new();

    // 缩放上限/下限
    public double MinScale { get; set; } = 0.05;
    public double MaxScale { get; set; } = 40.0;

    // 滚轮缩放倍率（每一档的乘子）
    public double WheelZoomStep { get; set; } = 1.1;

    // 默认光标
    private readonly Cursor _defaultCursor = new Cursor(StandardCursorType.Cross);

    // 右键拖拽时的状态
    private bool _isPanning;
    private Point _lastPointer;
    private Point _offset = new Point(0, 0);
    private double _scale = 1.0;

    // Interaction state
    private bool _isDrawingRect;
    private RectShape? _activeRect;
    private Point _drawStartImg;

    private bool _isDrawingPoly;
    private PolygonShape? _activePoly;

    private bool _isDrawingCircle;
    private CircleShape? _activeCircle;

    private bool _isDrawingLine;
    private LineShape? _activeLine;

    // Freehand drawing (pencil)
    private bool _isDrawingFreehand;
    private PolygonShape? _activeFreehand;

    // Eraser state
    private bool _isErasing;
    private bool _hasHover;
    private Point _hoverImg;

    private IShape? _hitShape;
    private int _hitHandleIndex;
    private bool _isDraggingHandle;
    private bool _isDraggingBody;
    private Point _lastImg;

    // 鼠标像素信息事件：在图像上移动时，通知外界当前坐标与RGB
    public event EventHandler<PixelInfoEventArgs>? PixelInfoChanged;

    private struct LastPixel
    {
        public int X;
        public int Y;
        public byte R;
        public byte G;
        public byte B;
        public bool InBounds;
    }
    private LastPixel _lastPixel;

    public RenderView()
    {
        Focusable = true; // 获得焦点
        ClipToBounds = true; // 超出边界不绘制

        RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.LowQuality);

        // 鼠标光标
        Cursor = _defaultCursor;
    }

    protected override void OnPropertyChanged(AvaloniaPropertyChangedEventArgs change)
    {
        base.OnPropertyChanged(change);
        if (change.Property == SourceProperty)
        {
            // 新图默认自适应"居中+适配最小边"
            FitImage();
            UpdateInterpolationMode();
            InvalidateMeasure();
            InvalidateVisual();
        }
        else if (change.Property == ZoomProperty)
        {
            // 仅用于指针坐标换算，绘制缩放由外层 LayoutTransformControl 负责
            InvalidateVisual();
        }
        else if (change.Property == StrokeProperty)
        {
            // Do not retroactively change existing or in-progress shapes
            InvalidateVisual();
        }
        else if (change.Property == StrokeThicknessProperty)
        {
            // Do not retroactively change existing or in-progress shapes
            InvalidateVisual();
        }
    }

    protected override Size MeasureOverride(Size availableSize)
    {
        if (Source is Bitmap bmp)
        {
            // 使用 Bitmap.Size（DIP 尺寸）避免 DPI 下像素/DIP 不一致导致的偏移
            return bmp.Size;
        }
        return base.MeasureOverride(availableSize);
    }

    public override void Render(DrawingContext context)
    {
        base.Render(context);

        DrawCheckerboard(context, Bounds, 18);

        if (Source is null) return;

        // 计算目标矩形：缩放 + 平移
        var iw = Source.Size.Width;
        var ih = Source.Size.Height;
        var dest = new Rect(_offset.X, _offset.Y, iw * _scale, ih * _scale);

        // 绘制图像（源矩形用原图大小）
        var src = new Rect(0, 0, iw, ih);

        if (_scale >= 9.0)
        {
            RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.None);
            context.DrawImage(Source, src, dest);
            if (ShowPixelGrid && _scale >= 10.0)
            {
                DrawPixelGrid(context, dest, (int)iw, (int)ih);
            }
        }
        else
        {
            RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.LowQuality);
            context.DrawImage(Source, src, dest);
        }

        var vp = new Viewport(_scale, _offset);
        // 仅绘制与视口相交的形状
        foreach (var s in Shapes)
        {
            if (!s.GetViewBounds(vp).Intersects(Bounds)) continue;
            s.Paint(context, vp);
        }

        // Draw eraser preview cursor: pretty cursor composed of a soft square area + eraser glyph
        if (Tool == ToolMode.Eraser && _hasHover)
        {
            var size = Math.Max(1, StrokeThickness);
            var half = size / 2.0;
            var rect = new Rect(_hoverImg.X - half, _hoverImg.Y - half, size, size);

            // Area preview with soft fill and crisp border
            var fill = new SolidColorBrush(Color.FromArgb(40, 255, 255, 255)); // ~16% white
            var border = new Pen(Brushes.White, 1);
            var shadow = new Pen(Brushes.Black, 2);
            context.DrawRectangle(fill, shadow, rect, 2, 2); // shadow first for subtle outline
            context.DrawRectangle(null, border, rect, 2, 2);

            // Simple eraser glyph (a tilted rounded rectangle) centered inside the area
            // Base glyph coordinates: a 12x8 rect, rotated -35 degrees, scaled relative to size
            double baseW = 12.0, baseH = 8.0;
            double scale = Math.Clamp(size / 24.0, 0.5, 2.5); // keep reasonable
            double w = baseW * scale, h = baseH * scale;
            double angle = -35.0 * Math.PI / 180.0;
            double cos = Math.Cos(angle), sin = Math.Sin(angle);

            Point Center(Point p)
                => new Point(_hoverImg.X + p.X * cos - p.Y * sin, _hoverImg.Y + p.X * sin + p.Y * cos);

            // Build a small 4-point polygon for the eraser body
            var p1 = Center(new Point(-w/2, -h/2));
            var p2 = Center(new Point( w/2, -h/2));
            var p3 = Center(new Point( w/2,  h/2));
            var p4 = Center(new Point(-w/2,  h/2));

            var geo = new StreamGeometry();
            using (var gc = geo.Open())
            {
                gc.BeginFigure(p1, true);
                gc.LineTo(p2);
                gc.LineTo(p3);
                gc.LineTo(p4);
                gc.EndFigure(true);
            }
            var glyphFill = new SolidColorBrush(Color.FromArgb(200, 245, 245, 245));
            var glyphPen = new Pen(Brushes.DimGray, 1);
            context.DrawGeometry(glyphFill, glyphPen, geo);

            // Small diagonal accent line to mimic eraser edge
            var acc1 = Center(new Point(-w*0.35, -h*0.15));
            var acc2 = Center(new Point( w*0.35,  h*0.15));
            context.DrawLine(new Pen(Brushes.Gray, 1), acc1, acc2);
        }
    }

    protected override void OnPointerMoved(PointerEventArgs e)
    {
        base.OnPointerMoved(e);
        var p = e.GetPosition(this);
        var img = ViewToImage(p);

        // 实时采样像素并上报
        RaisePixelInfo(img);

        // cursor: crosshair for drawing tools; none for eraser (we draw a custom square)
        if (Tool == ToolMode.DrawRect || Tool == ToolMode.DrawPolygon || Tool == ToolMode.DrawCircle || Tool == ToolMode.DrawLine || Tool == ToolMode.DrawFreehand)
            this.Cursor = new Cursor(StandardCursorType.Cross);
        else if (Tool == ToolMode.Eraser)
            this.Cursor = new Cursor(StandardCursorType.None);

        // track hover point for eraser preview
        _hasHover = true;
        _hoverImg = img;

        if (_isPanning)
        {
            var delta = p - _lastPointer;
            _lastPointer = p;

            var newOffset = new Point(_offset.X + delta.X, _offset.Y + delta.Y);
            _offset = ClampOffset(newOffset);
            InvalidateVisual();
            e.Handled = true;
            return;
        }

        if (_isDrawingRect && _activeRect != null)
        {
            var r = new Rect(_drawStartImg, img);
            _activeRect.Rect = NormalizeRect(r);
            InvalidateVisual();
            return;
        }
        if (_isDrawingPoly && _activePoly != null)
        {
            // 更新预览点（末尾点跟随鼠标）
            if (_activePoly.Points.Count >= 1)
            {
                _activePoly.Points[^1] = img;
                InvalidateVisual();
            }
            return;
        }
        if (_isDrawingFreehand && _activeFreehand != null)
        {
            // 仅在距离超过阈值时添加点，降低点数
            const double minDist = 0.5; // image space pixels
            if (_activeFreehand.Points.Count == 0)
            {
                _activeFreehand.Points.Add(img);
            }
            else
            {
                var last = _activeFreehand.Points[^1];
                var dx = img.X - last.X; var dy = img.Y - last.Y;
                if (dx * dx + dy * dy >= minDist * minDist)
                {
                    _activeFreehand.Points.Add(img);
                }
            }
            InvalidateVisual();
            return;
        }
        if (_isErasing)
        {
            EraseAt(img, StrokeThickness);
            InvalidateVisual();
            return;
        }
        if (_isDrawingCircle && _activeCircle != null)
        {
            // 改为通过拖拽矩形确定椭圆边界
            _activeCircle.SetBounds(new Rect(_drawStartImg, img));
            InvalidateVisual();
            return;
        }
        if (_isDrawingLine && _activeLine != null)
        {
            // 终点通过拖拽终点句柄实现
            _activeLine.MoveHandle(1, img);
            InvalidateVisual();
            return;
        }
        if (_isDraggingHandle && _hitShape != null)
        {
            _hitShape.MoveHandle(_hitHandleIndex, img);
            InvalidateVisual();
            return;
        }
        if (_isDraggingBody && _hitShape != null)
        {
            var d = img - _lastImg;
            _hitShape.Move(d);
            _lastImg = img;
            this.Cursor = new Cursor(StandardCursorType.SizeAll);
            InvalidateVisual();
            return;
        }
    }

    protected override void OnPointerExited(PointerEventArgs e)
    {
        base.OnPointerExited(e);
        _hasHover = false;
        InvalidateVisual();
        // avoid lingering SizeAll or custom cursors when leaving
        if (!_isDraggingBody && !_isDraggingHandle)
        {
            this.Cursor = new Cursor(StandardCursorType.Arrow);
        }
    }

    protected override void OnPointerPressed(PointerPressedEventArgs e)
    {
        base.OnPointerPressed(e);
        if (Source is null) return;

        var p = e.GetPosition(this);
        var img = ViewToImage(p);
        this.Focus();

        var props = e.GetCurrentPoint(this).Properties;
        if (props.IsRightButtonPressed)
        {
            _isPanning = true;
            _lastPointer = p;

            Cursor = new Cursor(StandardCursorType.Hand);

            e.Pointer.Capture(this);
            e.Handled = true;
            return;
        }

        if (props.IsLeftButtonPressed && e.ClickCount == 2) // 左键双击：回到适配
        {
            FitImage();
            e.Handled = true;
            return;
        }

        if (Tool == ToolMode.DrawRect)
        {
            // 优先命中已存在图形，支持在绘图工具下进行选择/编辑
            var tolSel = CalculateAdaptiveHitTolerance(_scale);
            var hitSel = HitTestShapes(img, tolSel);
            if (hitSel.Target != null)
            {
                _hitShape = hitSel.Target;
                _hitHandleIndex = hitSel.HandleIndex;
                ClearSelection();
                _hitShape.Selected = true;
                if (hitSel.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                {
                    (_hitShape as RectShape)?.BeginHandleDrag(_hitHandleIndex);
                    _isDraggingHandle = true;
                    e.Pointer.Capture(this);
                }
                else if (hitSel.Kind == HitKind.Body)
                {
                    _isDraggingBody = true;
                    _lastImg = img;
                    e.Pointer.Capture(this);
                }
                InvalidateVisual();
                return;
            }
            _isDrawingRect = true;
            _drawStartImg = img;
            _activeRect = new RectShape(new Rect(img, img))
            {
                Selected = true,
                Fill = null,
                BorderColor = Stroke,
                BorderThick = StrokeThickness
            };
            ClearSelection();
            Shapes.Add(_activeRect);
            InvalidateVisual();
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.DrawPolygon)
        {
            var polyProps = e.GetCurrentPoint(this).Properties;
            // 右键结束
            if (_isDrawingPoly && polyProps.IsRightButtonPressed)
            {
                FinishPolygonCommit();
                e.Pointer.Capture(null);
                return;
            }

            // 双击结束
            if (_isDrawingPoly && e.ClickCount >= 2)
            {
                FinishPolygonCommit();
                e.Pointer.Capture(null);
                return;
            }

            if (!_isDrawingPoly)
            {
                // 优先命中已存在图形，支持在绘图工具下进行选择/编辑
                var tolSel2 = 6.0 / Math.Max(_scale, 0.001);
                var hitSel2 = HitTestShapes(img, tolSel2);
                if (hitSel2.Target != null)
                {
                    _hitShape = hitSel2.Target;
                    _hitHandleIndex = hitSel2.HandleIndex;
                    ClearSelection();
                    _hitShape.Selected = true;
                    if (hitSel2.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                    {
                        (_hitShape as RectShape)?.BeginHandleDrag(_hitHandleIndex);
                        _isDraggingHandle = true;
                        e.Pointer.Capture(this);
                    }
                    else if (hitSel2.Kind == HitKind.Body)
                    {
                        _isDraggingBody = true;
                        _lastImg = img;
                        e.Pointer.Capture(this);
                    }
                    InvalidateVisual();
                    return;
                }
                _isDrawingPoly = true;
                _activePoly = new PolygonShape(new[] { img, img }, closed: true)
                {
                    Selected = true,
                    Fill = null,
                    BorderColor = Stroke,
                    BorderThick = StrokeThickness
                };
                ClearSelection();
                Shapes.Add(_activePoly);
                InvalidateVisual();
                e.Pointer.Capture(this);
                return;
            }
            else if (_activePoly != null)
            {
                // 添加一个新顶点，并附加一个跟随鼠标的预览点
                if (_activePoly.Points.Count >= 1)
                {
                    // 固定上一预览点为真实点，再添加新的预览点
                    _activePoly.Points[^1] = img;
                    _activePoly.Points.Add(img);
                    InvalidateVisual();
                    e.Pointer.Capture(this);
                    return;
                }
            }
        }

        if (Tool == ToolMode.DrawCircle)
        {
            // 优先命中已存在图形，支持在绘图工具下进行选择/编辑
            var tolSel3 = 6.0 / Math.Max(_scale, 0.001);
            var hitSel3 = HitTestShapes(img, tolSel3);
            if (hitSel3.Target != null)
            {
                _hitShape = hitSel3.Target;
                _hitHandleIndex = hitSel3.HandleIndex;
                ClearSelection();
                _hitShape.Selected = true;
                if (hitSel3.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                {
                    _isDraggingHandle = true;
                    e.Pointer.Capture(this);
                }
                else if (hitSel3.Kind == HitKind.Body)
                {
                    _isDraggingBody = true;
                    _lastImg = img;
                    e.Pointer.Capture(this);
                }
                InvalidateVisual();
                return;
            }
            _isDrawingCircle = true;
            _drawStartImg = img;
            _activeCircle = new CircleShape(new Rect(img, img))
            {
                Selected = true,
                Fill = null,
                BorderColor = Stroke,
                BorderThick = StrokeThickness
            };
            ClearSelection();
            Shapes.Add(_activeCircle);
            InvalidateVisual();
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.DrawLine)
        {
            // 优先命中已存在图形
            var tolSel4 = 6.0 / Math.Max(_scale, 0.001);
            var hitSel4 = HitTestShapes(img, tolSel4);
            if (hitSel4.Target != null)
            {
                _hitShape = hitSel4.Target;
                _hitHandleIndex = hitSel4.HandleIndex;
                ClearSelection();
                _hitShape.Selected = true;
                if (hitSel4.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                {
                    _isDraggingHandle = true;
                    e.Pointer.Capture(this);
                }
                else if (hitSel4.Kind == HitKind.Body)
                {
                    _isDraggingBody = true;
                    _lastImg = img;
                    e.Pointer.Capture(this);
                }
                InvalidateVisual();
                return;
            }
            _isDrawingLine = true;
            _activeLine = new LineShape(img, img)
            {
                Selected = true,
                Fill = null,
                BorderColor = Stroke,
                BorderThick = StrokeThickness
            };
            ClearSelection();
            Shapes.Add(_activeLine);
            InvalidateVisual();
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.DrawFreehand)
        {
            _isDrawingFreehand = true;
            _activeFreehand = new PolygonShape(new[] { img }, closed: false)
            {
                Selected = true,
                Fill = null,
                BorderColor = Stroke,
                BorderThick = StrokeThickness
            };
            _activeFreehand.ShowHandles = false; // hide handles for freehand
            ClearSelection();
            Shapes.Add(_activeFreehand);
            InvalidateVisual();
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.Eraser)
        {
            _isErasing = true;
            // perform initial erase at press
            EraseAt(img, StrokeThickness);
            InvalidateVisual();
            e.Pointer.Capture(this);
            return;
        }

        // Edit mode: hit test
        var tol = 6.0 / Math.Max(_scale, 0.001); // 使屏幕空间的命中半径大致恒定
        var hit = HitTestShapes(img, tol);
        _hitShape = hit.Target;
        _hitHandleIndex = hit.HandleIndex;
        ClearSelection();
        if (_hitShape != null)
        {
            _hitShape.Selected = true;
            if (hit.Kind == HitKind.Handle && _hitHandleIndex >= 0)
            {
                _isDraggingHandle = true;
                // 告知形状开始句柄拖拽，以记录锚点/角度等上下文
                (_hitShape as RectShape)?.BeginHandleDrag(_hitHandleIndex);
                e.Pointer.Capture(this);
            }
            else if (hit.Kind == HitKind.Body)
            {
                _isDraggingBody = true;
                _lastImg = img;
                e.Pointer.Capture(this);
            }
            InvalidateVisual();
        }
    }

    protected override void OnPointerReleased(PointerReleasedEventArgs e)
    {
        base.OnPointerReleased(e);

        if (_isPanning)
        {
            _isPanning = false;
            if (e.Pointer.Captured == this)
                e.Pointer.Capture(null);
            Cursor = _defaultCursor;
            e.Handled = true;
            return;
        }

        if (_isDrawingRect)
        {
            _isDrawingRect = false;
            _activeRect = null;
            // stay in DrawRect for continuous drawing
        }
        if (_isDrawingPoly)
        {
            // 多边形绘制使用点击添加点，不在释放时结束
        }
        if (_isDrawingCircle)
        {
            _isDrawingCircle = false;
            _activeCircle = null;
            // stay in DrawCircle
        }
        if (_isDrawingLine)
        {
            _isDrawingLine = false;
            _activeLine = null;
            // stay in DrawLine
        }
        if (_isDrawingFreehand)
        {
            _isDrawingFreehand = false;
            if (_activeFreehand != null)
            {
                // 若点数过少则丢弃
                if (_activeFreehand.Points.Count < 2)
                {
                    Shapes.Remove(_activeFreehand);
                }
            }
            _activeFreehand = null;
        }
        if (_isErasing)
        {
            _isErasing = false;
        }
        // 结束句柄拖拽会话，恢复内部状态（锚点/角度）
        if (_isDraggingHandle && _hitShape is RectShape rs)
        {
            rs.EndHandleDrag();
        }
        _isDraggingHandle = false;
        _isDraggingBody = false;
        _hitShape = null;
        _hitHandleIndex = 0;
        e.Pointer.Capture(null);
    }

    private void UpdateCursorForPoint(Point imgPt)
    {
        if (Tool == ToolMode.DrawRect)
        {
            Cursor = new Cursor(StandardCursorType.Cross);
            return;
        }
        var tol = 6.0 / Math.Max(_scale, 0.001);
        var hit = HitTestShapes(imgPt, tol);
        if (hit.Target is ShapeBase sb)
        {
            if (hit.Kind == HitKind.Handle)
            {
                var h = sb.Handles.FirstOrDefault(x => x.Index == hit.HandleIndex);
                Cursor = h?.Cursor ?? new Cursor(StandardCursorType.Arrow);
                return;
            }
            if (hit.Kind == HitKind.Body)
            {
                Cursor = new Cursor(StandardCursorType.SizeAll);
                return;
            }
        }
        Cursor = new Cursor(StandardCursorType.Arrow);
    }

    private HitResult HitTestShapes(Point imgPt, double tol)
    {
        for (int i = Shapes.Count - 1; i >= 0; --i)
        {
            var s = Shapes[i];
            var hit = s.HitTest(imgPt, tol);
            if (hit.Kind != HitKind.None) return hit;
        }
        return HitResult.None;
    }

    private Point ViewToImage(Point p)
    {
        // 使用内部缩放和平移状态进行坐标转换
        var vp = new Viewport(_scale, _offset);
        return vp.ViewToImage(p);
    }

    protected override void OnKeyDown(KeyEventArgs e)
    {
        base.OnKeyDown(e);
        if (e.Key == Key.R) // R 键重置
        {
            FitImage();
            e.Handled = true;
        }
        else if (e.Key == Key.Delete)
        {
            var any = false;
            for (int i = Shapes.Count - 1; i >= 0; --i)
            {
                if (Shapes[i].Selected)
                {
                    Shapes.RemoveAt(i);
                    any = true;
                }
            }
            if (any)
            {
                InvalidateVisual();
                e.Handled = true;
            }
        }
        else if (e.Key == Key.Escape && _isDrawingPoly)
        {
            // 取消当前多边形
            if (_activePoly != null)
            {
                Shapes.Remove(_activePoly);
            }
            _activePoly = null;
            _isDrawingPoly = false;
            InvalidateVisual();
            e.Handled = true;
        }
    }

    private void FinishPolygonCommit()
    {
        if (_activePoly == null)
        {
            _isDrawingPoly = false;
            return;
        }
        // 去掉拖尾的预览点
        if (_activePoly.Points.Count >= 2 && _activePoly.Points[^1] == _activePoly.Points[^2])
        {
            _activePoly.Points.RemoveAt(_activePoly.Points.Count - 1);
        }
        // 至少3点才闭合
        if (_activePoly.Points.Count < 3)
        {
            Shapes.Remove(_activePoly);
        }
        _activePoly = null;
        _isDrawingPoly = false;
        // 保持当前工具为 DrawPolygon，支持连续绘制
        InvalidateVisual();
    }

    private void ClearSelection()
    {
        foreach (var s in Shapes) s.Selected = false;
    }

    protected override void OnPointerEntered(PointerEventArgs e)
    {
        base.OnPointerEntered(e);
        if (Tool == ToolMode.DrawRect || Tool == ToolMode.DrawPolygon || Tool == ToolMode.DrawCircle || Tool == ToolMode.DrawLine)
            this.Cursor = new Cursor(StandardCursorType.Cross);
    }

    // (Duplicate OnPointerExited removed; logic consolidated in the earlier override)

    private static Rect NormalizeRect(Rect r)
    {
        var x1 = Math.Min(r.Left, r.Right);
        var x2 = Math.Max(r.Left, r.Right);
        var y1 = Math.Min(r.Top, r.Bottom);
        var y2 = Math.Max(r.Top, r.Bottom);
        return new Rect(new Point(x1, y1), new Point(x2, y2));
    }

    private void EraseAt(Point imgPt, double radius)
    {
        if (radius <= 0) radius = 1;
        double r2 = radius * radius;
        for (int i = Shapes.Count - 1; i >= 0; --i)
        {
            var s = Shapes[i];
            if (s is PolygonShape poly && !poly.Closed)
            {
                // Build contiguous segments of points that survive erasing
                var segments = new System.Collections.Generic.List<System.Collections.Generic.List<Point>>();
                System.Collections.Generic.List<Point>? current = null;
                for (int p = 0; p < poly.Points.Count; p++)
                {
                    var pt = poly.Points[p];
                    var dx = pt.X - imgPt.X; var dy = pt.Y - imgPt.Y;
                    bool erased = (dx * dx + dy * dy) <= r2;
                    if (!erased)
                    {
                        if (current == null)
                            current = new System.Collections.Generic.List<Point>();
                        current.Add(pt);
                    }
                    else
                    {
                        if (current != null && current.Count >= 2)
                        {
                            segments.Add(current);
                        }
                        current = null;
                    }
                }
                if (current != null && current.Count >= 2)
                {
                    segments.Add(current);
                }

                // Replace the original polyline with the kept segments
                if (segments.Count == 0)
                {
                    Shapes.RemoveAt(i);
                    continue;
                }
                else if (segments.Count == 1)
                {
                    // mutate in place for minimal churn
                    poly.Points.Clear();
                    poly.Points.AddRange(segments[0]);
                }
                else
                {
                    // remove original and insert new pieces
                    Shapes.RemoveAt(i);
                    foreach (var seg in segments)
                    {
                        var piece = new PolygonShape(seg, closed: false)
                        {
                            Selected = false,
                            Fill = null,
                            BorderColor = poly.BorderColor,
                            BorderThick = poly.BorderThick,
                            ShowHandles = poly.ShowHandles
                        };
                        Shapes.Insert(i, piece);
                        i++; // advance insertion index to retain order
                    }
                }
            }
            else
            {
                // hit-test with tolerance; remove entire shape when hit
                var hit = s.HitTest(imgPt, radius);
                if (hit.Kind != HitKind.None)
                {
                    Shapes.RemoveAt(i);
                }
            }
        }
    }

    protected override void OnPointerWheelChanged(PointerWheelEventArgs e)
    {
        base.OnPointerWheelChanged(e);
        if (Source is null) return;

        // 检查是否按下Ctrl键
        var keyModifiers = e.KeyModifiers;
        bool isCtrlPressed = keyModifiers.HasFlag(KeyModifiers.Control);

        if (isCtrlPressed)
        {
            // Ctrl+滚轮：缩放
            var p = e.GetPosition(this);

            // 计算新缩放
            double factor = e.Delta.Y > 0 ? WheelZoomStep : 1.0 / WheelZoomStep;
            double oldScale = _scale;
            double newScale = Math.Clamp(_scale * factor, MinScale, MaxScale);

            if (Math.Abs(newScale - oldScale) < 1e-6)
                return;

            // 保持光标下的图像点不漂移：offset' = p - (p - offset) * (s'/s)
            var sx = newScale / oldScale;
            var newOffset = new Point(
                p.X - (p.X - _offset.X) * sx,
                p.Y - (p.Y - _offset.Y) * sx
            );

            _scale = newScale;
            _offset = ClampOffset(newOffset);

            UpdateInterpolationMode();
            InvalidateVisual();
            e.Handled = true;
        }
        else
        {
            // 普通滚轮：垂直平移
            double panStep = 50.0; // 平移步长
            double deltaY = e.Delta.Y > 0 ? -panStep : panStep;

            var newOffset = new Point(_offset.X, _offset.Y + deltaY);
            _offset = ClampOffset(newOffset);
            InvalidateVisual();
            e.Handled = true;
        }
    }

    protected override void OnSizeChanged(SizeChangedEventArgs e)
    {
        base.OnSizeChanged(e);
        // 你也可以选择 Size 变化时保持当前视图；这里不自动Fit，除非首次加载。
        if (Source != null && _scale == 1.0 && _offset == default)
        {
            FitImage();
        }
        UpdateInterpolationMode();
    }

    public void FitImage()
    {
        if (Source is null || Bounds.Width <= 0 || Bounds.Height <= 0)
        {
            _scale = 1.0;
            _offset = new Point(0, 0);
            InvalidateVisual();
            return;
        }

        // 按较小的缩放适配到控件区域并居中
        double vw = Bounds.Width;
        double vh = Bounds.Height;
        double iw = Source.Size.Width;   // DIP
        double ih = Source.Size.Height;  // DIP

        double s = Math.Min(vw / iw, vh / ih);
        s = Math.Clamp(s, MinScale, MaxScale);

        _scale = s;
        double dx = (vw - iw * s) / 2.0;
        double dy = (vh - ih * s) / 2.0;
        var newOffset = new Point(dx, dy);
        _offset = ClampOffset(newOffset);

        InvalidateVisual();
    }

    private void UpdateInterpolationMode()
    {
        var mode = (_scale > 10.0)
            ? BitmapInterpolationMode.None   // 放大：像素级
            : BitmapInterpolationMode.LowQuality;    // 其他：正常插值

        RenderOptions.SetBitmapInterpolationMode(this, mode);
    }

    private void DrawPixelGrid(DrawingContext ctx, Rect dest, int iw, int ih)
    {
        double renderScaling = (this.VisualRoot as TopLevel)?.RenderScaling ?? 1.0;
        double thicknessDIP = GridLineThicknessInDevicePixels / renderScaling;

        var pen = new Pen(GridLineBrush, thicknessDIP);

        // 仅绘制视口内的像素线，减少循环次数
        var view = Bounds.Intersect(dest);

        int x0 = Math.Max(0, (int)Math.Floor((view.X - dest.X) / _scale));
        int x1 = Math.Min(iw, (int)Math.Ceiling((view.Right - dest.X) / _scale));
        int y0 = Math.Max(0, (int)Math.Floor((view.Y - dest.Y) / _scale));
        int y1 = Math.Min(ih, (int)Math.Ceiling((view.Bottom - dest.Y) / _scale));

        // 像素对齐：把线的位置吸附到设备像素栅格，线更锐利
        static double Snap(double x, double rs) => Math.Round(x * rs) / rs;

        // 竖线（像素列分隔）
        for (int xi = x0; xi <= x1; xi++)
        {
            double x = dest.X + xi * _scale;
            double xs = Snap(x, renderScaling);
            ctx.DrawLine(pen, new Point(xs, view.Y), new Point(xs, view.Bottom));
        }

        // 横线（像素行分隔）
        for (int yi = y0; yi <= y1; yi++)
        {
            double y = dest.Y + yi * _scale;
            double ys = Snap(y, renderScaling);
            ctx.DrawLine(pen, new Point(view.X, ys), new Point(view.Right, ys));
        }
    }

    private void DrawCheckerboard(DrawingContext ctx, Rect bounds, int cell = 18)
    {
        var light = new SolidColorBrush(Color.FromRgb(50, 50, 50));
        var dark  = new SolidColorBrush(Color.FromRgb(35, 35, 35));
        for (int y = 0; y < bounds.Height; y += cell)
        {
            for (int x = 0; x < bounds.Width; x += cell)
            {
                bool odd = ((x / cell) + (y / cell)) % 2 == 1;
                ctx.FillRectangle(odd ? dark : light,
                    new Rect(x, y, Math.Min(cell, bounds.Width - x), Math.Min(cell, bounds.Height - y)));
            }
        }
    }

    /// <summary>
    /// 限制偏移量，确保图像不会完全移出控件边界
    /// 允许图像部分超出边界，但至少保持一部分可见
    /// </summary>
    private Point ClampOffset(Point offset)
    {
        if (Source is null || Bounds.Width <= 0 || Bounds.Height <= 0)
            return offset;

        // 计算图像在当前缩放下的尺寸
        double imageWidth = Source.Size.Width * _scale;
        double imageHeight = Source.Size.Height * _scale;

        // 控件尺寸
        double viewWidth = Bounds.Width;
        double viewHeight = Bounds.Height;

        // 定义最小可见区域（图像的一小部分必须保持可见）
        double minVisibleWidth = Math.Min(100, imageWidth * 0.1);   // 至少100像素或图像宽度的10%
        double minVisibleHeight = Math.Min(100, imageHeight * 0.1); // 至少100像素或图像高度的10%

        // 计算允许的偏移范围
        double minX, maxX, minY, maxY;

        if (imageWidth <= viewWidth)
        {
            // 图像比控件窄，居中显示
            double centerX = (viewWidth - imageWidth) / 2;
            minX = maxX = centerX;
        }
        else
        {
            // 图像比控件宽，允许平移但保持部分可见
            minX = viewWidth - imageWidth - minVisibleWidth;  // 图像右边缘可以超出，但要保持左边部分可见
            maxX = minVisibleWidth;                           // 图像左边缘可以超出，但要保持右边部分可见
        }

        if (imageHeight <= viewHeight)
        {
            // 图像比控件矮，居中显示
            double centerY = (viewHeight - imageHeight) / 2;
            minY = maxY = centerY;
        }
        else
        {
            // 图像比控件高，允许平移但保持部分可见
            minY = viewHeight - imageHeight - minVisibleHeight; // 图像底边缘可以超出，但要保持顶部部分可见
            maxY = minVisibleHeight;                            // 图像顶边缘可以超出，但要保持底部部分可见
        }

        // 限制偏移量
        double clampedX = Math.Clamp(offset.X, minX, maxX);
        double clampedY = Math.Clamp(offset.Y, minY, maxY);

        return new Point(clampedX, clampedY);
    }

    // 将图像坐标转换为像素并读取RGB，触发事件
    private void RaisePixelInfo(Point imgPos)
    {
        if (Source is null)
        {
            _lastPixel = new LastPixel { X = -1, Y = -1, R = 0, G = 0, B = 0, InBounds = false };
            PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(-1, -1, 0, 0, 0, false));
            InvalidateVisual();
            return;
        }

        // 将DIP坐标映射到实际像素（考虑位图DPI缩放）
        double scaleX = Source.PixelSize.Width / Source.Size.Width;
        double scaleY = Source.PixelSize.Height / Source.Size.Height;
        int px = (int)Math.Floor(imgPos.X * scaleX);
        int py = (int)Math.Floor(imgPos.Y * scaleY);

        bool inBounds = px >= 0 && py >= 0 && px < Source.PixelSize.Width && py < Source.PixelSize.Height;
        if (!inBounds)
        {
            _lastPixel = new LastPixel { X = px, Y = py, R = 0, G = 0, B = 0, InBounds = false };
            PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(px, py, 0, 0, 0, false));
            InvalidateVisual();
            return;
        }

        byte r = 0, g = 0, b = 0;
        // 通过 CopyPixels 读取单像素（BGRA 32bpp）
        var rect = new PixelRect(px, py, 1, 1);
        var buf = new byte[4];
        var handle = GCHandle.Alloc(buf, GCHandleType.Pinned);
        try
        {
            Source.CopyPixels(rect, handle.AddrOfPinnedObject(), 4, 4);
            b = buf[0];
            g = buf[1];
            r = buf[2];
        }
        finally
        {
            if (handle.IsAllocated) handle.Free();
        }

        _lastPixel = new LastPixel { X = px, Y = py, R = r, G = g, B = b, InBounds = true };
        PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(px, py, r, g, b, true));
        InvalidateVisual();
    }

    public sealed class PixelInfoEventArgs : EventArgs
    {
        public int X { get; }
        public int Y { get; }
        public byte R { get; }
        public byte G { get; }
        public byte B { get; }
        public bool InBounds { get; }

        public PixelInfoEventArgs(int x, int y, byte r, byte g, byte b, bool inBounds)
        {
            X = x; Y = y; R = r; G = g; B = b; InBounds = inBounds;
        }
    }
}
