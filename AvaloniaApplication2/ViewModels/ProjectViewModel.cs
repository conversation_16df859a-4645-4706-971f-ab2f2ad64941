using AvaloniaApplication2.Models;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Linq;

namespace AvaloniaApplication2.ViewModels
{
    public partial class ProjectViewModel : ViewModelBase
    {
        [ObservableProperty]
        private ProjectItem? _selectedProject;

        public ObservableCollection<ProjectItem> RecentProjects { get; }
        public ObservableCollection<ProjectItem> AllProjects { get; }

        public IRelayCommand<ProjectItem> SelectProjectCommand { get; }

        public ProjectViewModel()
        {
            RecentProjects = new ObservableCollection<ProjectItem>
            {
                new ProjectItem 
                {
                    ImagePath = "/Assets/Images/pills.png", 
                    Name = "Example Instance Segmentation Pills", 
                    Description = "Example project for the instance segmentation of different pills in a bag.",
                    ProjectPath = "C:/Users/<USER>/AppData/Roaming/MVTec/.../Example_InstanceSegm_PillBags.dtlp",
                    ImageTags = "10: Omega-3, KMW, Stomach tablet, Ginko, Ginseng, Glucosamin, Cognivia, Capsularum I, iron tablet, Vitamin-B"
                },
                new ProjectItem { ImagePath = "/Assets/Images/template.jpg", Name = "star_seg" },
                new ProjectItem { ImagePath = "/Assets/Images/anomaly.png", Name = "defect_detect" },
                new ProjectItem { ImagePath = "/Assets/Images/semantic.png", Name = "Example Semantic Segmentation" }
            };

            AllProjects = new ObservableCollection<ProjectItem>
            {
                new ProjectItem { ImagePath = "/Assets/Images/anomaly.png", Name = "Example Anomaly Detection" },
                new ProjectItem { ImagePath = "/Assets/Images/fruits.png", Name = "Example Classification Fruits" },
                new ProjectItem { ImagePath = "/Assets/Images/ocr.png", Name = "Example Deep OCR" },
                new ProjectItem { ImagePath = "/Assets/Images/pills.png", Name = "Example Instance Segmentation" },
                new ProjectItem { ImagePath = "/Assets/Images/object_detection.png", Name = "Example Oriented Object Detection" },
                new ProjectItem { ImagePath = "/Assets/Images/pills_2.png", Name = "Example Object Detection Pill" },
                new ProjectItem { ImagePath = "/Assets/Images/semantic.png", Name = "Example Semantic Segmentation" }
            };

            SelectProjectCommand = new RelayCommand<ProjectItem>(p => SelectedProject = p);

            SelectedProject = RecentProjects.FirstOrDefault();
        }
    }
}
