using System;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using CommunityToolkit.Mvvm.Input;
using AvaloniaApplication2.Models;

namespace AvaloniaApplication2.ViewModels
{
    public class InspectViewModel : ViewModelBase
    {
        // Reference to the Gallery VM to share state (items, tags, thumbnail size)
        private GalleryViewModel? _galleryRef;
        public GalleryViewModel? GalleryRef
        {
            get => _galleryRef;
            set
            {
                if (!ReferenceEquals(_galleryRef, value))
                {
                    var old = _galleryRef;
                    if (old != null)
                    {
                        // Unhook from old
                        _galleryRef = old; // temp set for helper
                        HookGalleryEvents(false);
                    }
                    // Assign new and hook
                    if (SetProperty(ref _galleryRef, value))
                    {
                        HookGalleryEvents(true);
                        RebuildFiltered();
                        OnPropertyChanged(nameof(Tags));
                        OnPropertyChanged(nameof(ThumbnailSize));
                    }
                }
            }
        }

        // Items with TagId > 0 only
        public ObservableCollection<GalleryItem> FilteredItems { get; } = new();

        // Mirror Gallery style
        public int ThumbnailSize
        {
            get => GalleryRef?.ThumbnailSize ?? 138;
            set
            {
                if (GalleryRef != null && GalleryRef.ThumbnailSize != value)
                {
                    GalleryRef.ThumbnailSize = value;
                    OnPropertyChanged();
                }
            }
        }

        // Share Tags with Gallery
        public ObservableCollection<TagCategory> Tags => GalleryRef?.Tags ?? new ObservableCollection<TagCategory>();

        // Selection/current
        private GalleryItem? _currentItem;
        public GalleryItem? CurrentItem
        {
            get => _currentItem;
            set => SetProperty(ref _currentItem, value);
        }

        // Commands
        public IRelayCommand<GalleryItem> ToggleSelectCommand { get; }
        public IRelayCommand<TagCategory> AssignTagToSelectedCommand { get; }

        public InspectViewModel()
        {
            ToggleSelectCommand = new RelayCommand<GalleryItem>(item =>
            {
                if (item == null) return;
                item.IsSelected = !item.IsSelected;
                CurrentItem = item;
            });
            AssignTagToSelectedCommand = new RelayCommand<TagCategory>(tag =>
            {
                if (tag == null) return;
                foreach (var it in FilteredItems.Where(i => i.IsSelected))
                {
                    it.TagId = tag.Id; // Changing TagId may drop/add from filter; handled by events
                }
            });
        }

        private void HookGalleryEvents(bool attach)
        {
            if (GalleryRef == null) return;
            if (attach)
            {
                GalleryRef.GalleryItems.CollectionChanged += OnGalleryItemsChanged;
                foreach (var it in GalleryRef.GalleryItems)
                    it.PropertyChanged += OnGalleryItemPropertyChanged;
            }
            else
            {
                GalleryRef.GalleryItems.CollectionChanged -= OnGalleryItemsChanged;
                foreach (var it in GalleryRef.GalleryItems)
                    it.PropertyChanged -= OnGalleryItemPropertyChanged;
            }
        }

        private void OnGalleryItemsChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                foreach (var obj in e.NewItems)
                {
                    if (obj is GalleryItem gi)
                    {
                        gi.PropertyChanged += OnGalleryItemPropertyChanged;
                        if (gi.TagId > 0) FilteredItems.Add(gi);
                    }
                }
            }
            if (e.Action == NotifyCollectionChangedAction.Remove && e.OldItems != null)
            {
                foreach (var obj in e.OldItems)
                {
                    if (obj is GalleryItem gi)
                    {
                        gi.PropertyChanged -= OnGalleryItemPropertyChanged;
                        FilteredItems.Remove(gi);
                    }
                }
            }
            if (e.Action == NotifyCollectionChangedAction.Reset)
            {
                RebuildFiltered();
            }
        }

        private void OnGalleryItemPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (sender is not GalleryItem gi) return;
            if (e.PropertyName == nameof(GalleryItem.TagId))
            {
                bool inList = FilteredItems.Contains(gi);
                if (gi.TagId > 0 && !inList)
                {
                    // Newly labeled -> add
                    FilteredItems.Add(gi);
                }
                else if (gi.TagId <= 0 && inList)
                {
                    // Cleared label -> remove
                    FilteredItems.Remove(gi);
                }
            }
        }

        private void RebuildFiltered()
        {
            FilteredItems.Clear();
            var src = GalleryRef?.GalleryItems;
            if (src == null) return;
            foreach (var gi in src)
            {
                if (gi.TagId > 0) FilteredItems.Add(gi);
            }
        }
    }
}
