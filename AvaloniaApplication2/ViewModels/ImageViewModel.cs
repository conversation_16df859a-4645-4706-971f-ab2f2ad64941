using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AvaloniaApplication2.Services;
using AvaloniaApplication2.Models;
using System.Linq;

namespace AvaloniaApplication2.ViewModels
{
    public class InstanceItem
    {
        public string Color { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public int Area { get; set; }
    }

    public partial class ImageViewModel : ViewModelBase
    {
        [ObservableProperty]
        private string? _currentImagePath;

        [ObservableProperty]
        private int _currentTagId; // 0 = 无标签，仅用于显示徽标

        // Display image after edits (brightness/contrast/opacity)
        [ObservableProperty]
        private string? _displayImagePath;

        // Factors
        [ObservableProperty]
        private float _opacityFactor = 1.0f; // 0..1

        [ObservableProperty]
        private float _brightnessFactor = 1.0f; // around 0.5..1.5

        [ObservableProperty]
        private float _contrastFactor = 1.0f; // around 0.5..1.5

        // Preview zoom for navigator (1.0 = 100%)
        [ObservableProperty]
        private double _previewZoom = 1.0;

        // Zoom percent text helper
        public int ZoomPercent => (int)Math.Round(PreviewZoom * 100);

        // Viewport (fractional values 0..1) of the main image within its scroll viewer
        [ObservableProperty] private double _viewportX;
        [ObservableProperty] private double _viewportY;
        [ObservableProperty] private double _viewportW = 1.0;
        [ObservableProperty] private double _viewportH = 1.0;

        public ObservableCollection<InstanceItem> InstanceItems { get; }
        // Shared with GalleryViewModel (we maintain same schema)
        public ObservableCollection<TagCategory> Tags { get; set; } = new();

        // Tag presets and creation fields (mirrors GalleryViewModel)
        public List<string> PresetColors { get; } = new() { "#9E9E9E", "#4CAF50", "#FFC107", "#2196F3", "#E91E63", "#FF5722", "#9C27B0", "#00BCD4" };
        public List<string> PresetIcons { get; } = new()
        {
            "TagOutline",
            "CheckBold",
            "AlertCircleOutline",
            "BugOutline",
            "FlagOutline",
            "LabelOutline",
            "Image",
        };
        private int _nextTagId = 1; // 0 reserved for 无标签
        [ObservableProperty] private string _newTagName = string.Empty;
        [ObservableProperty] private string _newTagColor = "#4CAF50";
        [ObservableProperty] private string _newTagIcon = "TagOutline";
        [ObservableProperty] private bool _newTagIsOk;

        // Commands
        public IRelayCommand<TagCategory> AssignTagToCurrentCommand { get; }
        public IRelayCommand AddTagCommand { get; }
        public IRelayCommand<TagCategory> DeleteTagCommand { get; }

        // Reference to gallery for progress syncing
        public GalleryViewModel? GalleryRef { get; set; }

        private readonly IImageEditService _imageEditService = new ImageEditService();
        public ImageDrawingService DrawingService { get; } = new ImageDrawingService();

        [ObservableProperty]
        private ToolMode _currentTool = ToolMode.Pan;

        public ImageViewModel()
        {
            InstanceItems = new ObservableCollection<InstanceItem>
            {
                new InstanceItem { Color = "#C51A4A", Name = "Cognivia", Type = "掩膜", Area = 10677 },
                new InstanceItem { Color = "#F34B28", Name = "Ginko", Type = "掩膜", Area = 14339 },
                new InstanceItem { Color = "#00A099", Name = "KMW", Type = "多边形", Area = 10171 },
                new InstanceItem { Color = "#008000", Name = "Ginseng", Type = "掩膜", Area = 5319 },
                new InstanceItem { Color = "#800000", Name = "Iron tablet", Type = "掩膜", Area = 5301 },
                new InstanceItem { Color = "#800080", Name = "Stomach tablet", Type = "掩膜", Area = 13306 },
                new InstanceItem { Color = "#0000FF", Name = "Vitamin-B", Type = "掩膜", Area = 14252 },
                new InstanceItem { Color = "#FFFF00", Name = "Omega-3", Type = "掩膜", Area = 19751 },
                new InstanceItem { Color = "#FFC0CB", Name = "Capsularum I", Type = "掩膜", Area = 13001 },
            };

            // Default tags: ensure 0 exists
            if (!Tags.Any(t => t.Id == 0))
            {
                Tags.Add(new TagCategory { Id = 0, Name = "无标签", Color = "#616161", IconKind = "TagOutline" });
            }

            AssignTagToCurrentCommand = new RelayCommand<TagCategory>(AssignTagToCurrent);
            AddTagCommand = new RelayCommand(AddTag);
            DeleteTagCommand = new RelayCommand<TagCategory>(DeleteTag);
        }

        partial void OnCurrentImagePathChanged(string? value)
        {
            UpdateDisplayImagePath();
        }

        partial void OnPreviewZoomChanged(double value)
        {
            OnPropertyChanged(nameof(ZoomPercent));
        }

        partial void OnOpacityFactorChanged(float value)
        {
            UpdateDisplayImagePath();
        }

        partial void OnBrightnessFactorChanged(float value)
        {
            UpdateDisplayImagePath();
        }

        partial void OnContrastFactorChanged(float value)
        {
            UpdateDisplayImagePath();
        }

        private void UpdateDisplayImagePath()
        {
            if (string.IsNullOrWhiteSpace(CurrentImagePath))
            {
                DisplayImagePath = CurrentImagePath;
                // Clear any display override if no image
                if (GalleryRef != null && !string.IsNullOrWhiteSpace(CurrentImagePath))
                {
                    var gi = GalleryRef.GalleryItems?.FirstOrDefault(i => string.Equals(i.ImagePath, CurrentImagePath, StringComparison.OrdinalIgnoreCase));
                    if (gi != null) gi.DisplayPath = null;
                }
                return;
            }

            try
            {
                var path = CurrentImagePath!;
                // Apply brightness first, then contrast, then opacity
                bool edited = false;
                if (Math.Abs(BrightnessFactor - 1.0f) > 0.001f)
                {
                    path = _imageEditService.AdjustBrightness(path, BrightnessFactor);
                    edited = true;
                }
                if (Math.Abs(ContrastFactor - 1.0f) > 0.001f)
                {
                    path = _imageEditService.AdjustContrast(path, ContrastFactor);
                    edited = true;
                }
                if (Math.Abs(OpacityFactor - 1.0f) > 0.001f)
                {
                    path = _imageEditService.AdjustOpacity(path, OpacityFactor);
                    edited = true;
                }

                DisplayImagePath = path;

                // Sync to gallery item display path
                if (GalleryRef != null)
                {
                    var gi = GalleryRef.GalleryItems?.FirstOrDefault(i => string.Equals(i.ImagePath, CurrentImagePath, StringComparison.OrdinalIgnoreCase));
                    if (gi != null)
                        gi.DisplayPath = edited ? path : null;
                }
            }
            catch
            {
                DisplayImagePath = CurrentImagePath;
                // On error, don't override gallery
            }
        }

        // ===== Tagging operations =====
        private void AssignTagToCurrent(TagCategory? tag)
        {
            if (tag == null) return;
            CurrentTagId = tag.Id;
            if (GalleryRef != null && !string.IsNullOrWhiteSpace(CurrentImagePath))
            {
                var gi = GalleryRef.GalleryItems?.FirstOrDefault(i => string.Equals(i.ImagePath, CurrentImagePath, StringComparison.OrdinalIgnoreCase));
                if (gi != null)
                {
                    gi.TagId = tag.Id;
                }
            }
        }

        private void AddTag()
        {
            var name = string.IsNullOrWhiteSpace(NewTagName) ? $"Tag_{_nextTagId}" : NewTagName.Trim();
            // NewTagIsOk chooses icon as ThumbUp/ThumbDown when toggled externally
            var icon = string.IsNullOrWhiteSpace(NewTagIcon) ? (NewTagIsOk ? "ThumbUp" : "ThumbDown") : NewTagIcon;
            var tag = new TagCategory
            {
                Id = _nextTagId++,
                Name = name,
                Color = NewTagColor,
                IconKind = icon
            };
            Tags.Add(tag);
            NewTagName = string.Empty;
            OnPropertyChanged(nameof(Tags));
        }

        private void DeleteTag(TagCategory? tag)
        {
            if (tag == null) return;
            if (tag.Id == 0) return;
            var removedId = tag.Id;
            Tags.Remove(tag);

            // Reindex like GalleryViewModel so IDs are dense
            var map = new Dictionary<int, int>();
            map[0] = 0;
            int next = 1;
            foreach (var t in Tags.OrderBy(t => t.Id).ToList())
            {
                if (t.Id == 0) continue;
                map[t.Id] = next;
                t.Id = next;
                next++;
            }

            // Remap current tag if necessary
            if (CurrentTagId == removedId) CurrentTagId = 0;
            else if (map.TryGetValue(CurrentTagId, out var newId)) CurrentTagId = newId;

            // Remap gallery items as well
            if (GalleryRef != null)
            {
                foreach (var it in GalleryRef.GalleryItems)
                {
                    if (it.TagId == removedId) it.TagId = 0;
                    else if (map.TryGetValue(it.TagId, out var newGiId)) it.TagId = newGiId;
                }
            }

            _nextTagId = next;
        }
    }
}
