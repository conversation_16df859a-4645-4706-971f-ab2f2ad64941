using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;
using System.IO;

namespace AvaloniaApplication2.ViewModels
{
    public class BoolToBrushConverter : IValueConverter
    {
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return new SolidColorBrush(Colors.Green);
            }
            return new SolidColorBrush(Colors.Transparent);
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BoolToThicknessConverter : IValueConverter
    {
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return new Avalonia.Thickness(2);
            }
            return new Avalonia.Thickness(0);
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Returns a bool suitable for IsVisible binding; supports invert when parameter == "False"
    public class BoolToVisibilityConverter : IValueConverter
    {
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            var flag = value is bool b && b;
            var invert = parameter is string s && s.Equals("False", StringComparison.OrdinalIgnoreCase);
            return invert ? !flag : flag;
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Shows prefix of filename while preserving extension, e.g. 'longfilename...jpg'
    // ConverterParameter: integer for max prefix length (default 12)
    public class FilenamePreviewConverter : IValueConverter
    {
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            var name = value as string ?? string.Empty;
            if (string.IsNullOrEmpty(name)) return string.Empty;

            var ext = Path.GetExtension(name); // includes dot
            var stem = Path.GetFileNameWithoutExtension(name);

            int maxPrefix = 12;
            if (parameter is string ps && int.TryParse(ps, out var parsed))
                maxPrefix = Math.Max(1, parsed);

            if (stem.Length <= maxPrefix)
                return stem + ext;

            var prefix = stem.Substring(0, maxPrefix);
            return prefix + "…" + ext;
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
