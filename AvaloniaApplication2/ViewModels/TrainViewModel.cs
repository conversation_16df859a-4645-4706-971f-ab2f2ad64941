using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace AvaloniaApplication2.ViewModels
{
    public partial class TrainViewModel : ViewModelBase
    {
        [ObservableProperty]
        private bool _isSettingsVisible = true;

        [ObservableProperty]
        private bool _isResultsVisible = false;

        public TrainViewModel()
        {
        }

        [RelayCommand]
        private void ShowSettings()
        {
            IsSettingsVisible = true;
            IsResultsVisible = false;
        }

        [RelayCommand]
        private void ShowResults()
        {
            IsSettingsVisible = false;
            IsResultsVisible = true;
        }
    }
}
