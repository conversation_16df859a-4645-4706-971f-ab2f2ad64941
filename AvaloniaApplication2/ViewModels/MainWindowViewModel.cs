using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using AvaloniaApplication2.Messages;
using System;
using System.Linq;

namespace AvaloniaApplication2.ViewModels
{
    public partial class MainWindowViewModel : ViewModelBase
    {
        public enum ViewTab
        {
            Project,
            Gallery,
            Image,
            Inspect,
            Split,
            Train,
            Evaluate,
            Export
        }

        [ObservableProperty]
        private ViewTab _currentTab;

        [ObservableProperty]
        private ViewModelBase _contentViewModel;

        // ViewModel instances for caching
        private ProjectViewModel _projectViewModel;
        private GalleryViewModel _galleryViewModel;
        private ImageViewModel _imageViewModel;
        private InspectViewModel _inspectViewModel;
        private SplitViewModel _splitViewModel;
        private TrainViewModel _trainViewModel;
        private EvaluateViewModel _evaluateViewModel;
        private ExportViewModel _exportViewModel;

        public MainWindowViewModel()
        {
            // Set the initial view
            ProjectView();

            // Listen to gallery double-tap open image requests
            WeakReferenceMessenger.Default.Register<OpenImageMessage>(this, (r, m) =>
            {
                // Ensure both VMs exist
                _galleryViewModel ??= new GalleryViewModel();
                _imageViewModel ??= new ImageViewModel();
                // Share Tags collection reference so they stay in sync
                _imageViewModel.Tags = _galleryViewModel.Tags;
                // Provide gallery reference for progress syncing
                _imageViewModel.GalleryRef = _galleryViewModel;
                // Set image and switch to Image view
                _imageViewModel.CurrentImagePath = m.Value;
                // Sync current tag badge from gallery item
                var gi = _galleryViewModel.GalleryItems.FirstOrDefault(i => string.Equals(i.ImagePath, m.Value, StringComparison.OrdinalIgnoreCase));
                _imageViewModel.CurrentTagId = gi?.TagId ?? 0;
                ContentViewModel = _imageViewModel;
                CurrentTab = ViewTab.Image;
            });
        }

        [RelayCommand]
        private void ProjectView()
        {
            ContentViewModel = _projectViewModel ??= new ProjectViewModel();
            CurrentTab = ViewTab.Project;
        }

        [RelayCommand]
        private void GalleryView()
        {
            ContentViewModel = _galleryViewModel ??= new GalleryViewModel();
            CurrentTab = ViewTab.Gallery;
        }

        [RelayCommand]
        private void ImageView()
        {
            _galleryViewModel ??= new GalleryViewModel();
            _imageViewModel ??= new ImageViewModel();
            // Keep tags consistent with gallery
            _imageViewModel.Tags = _galleryViewModel.Tags;
            _imageViewModel.GalleryRef = _galleryViewModel;
            // Do NOT preset tag badge here; only show after user double-clicks in gallery
            _imageViewModel.CurrentTagId = 0;
            ContentViewModel = _imageViewModel;
            CurrentTab = ViewTab.Image;
        }

        [RelayCommand]
        private void InspectView()
        {
            // Ensure gallery exists to supply data (items/tags)
            _galleryViewModel ??= new GalleryViewModel();
            _inspectViewModel ??= new InspectViewModel();
            _inspectViewModel.GalleryRef = _galleryViewModel;
            ContentViewModel = _inspectViewModel;
            CurrentTab = ViewTab.Inspect;
        }

        [RelayCommand]
        private void SplitView()
        {
            _galleryViewModel ??= new GalleryViewModel();
            _splitViewModel ??= new SplitViewModel();
            _splitViewModel.GalleryRef = _galleryViewModel;
            ContentViewModel = _splitViewModel;
            CurrentTab = ViewTab.Split;
        }

        [RelayCommand]
        private void TrainView()
        {
            ContentViewModel = _trainViewModel ??= new TrainViewModel();
            CurrentTab = ViewTab.Train;
        }

        [RelayCommand]
        private void EvaluateView()
        {
             ContentViewModel = _evaluateViewModel ??= new EvaluateViewModel();
             CurrentTab = ViewTab.Evaluate;
        }

        [RelayCommand]
        private void ExportView()
        {
             ContentViewModel = _exportViewModel ??= new ExportViewModel();
             CurrentTab = ViewTab.Export;
        }
    }
}
