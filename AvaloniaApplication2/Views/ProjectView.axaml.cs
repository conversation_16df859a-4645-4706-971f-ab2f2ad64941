using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.VisualTree;
using AvaloniaApplication2.Models;
using AvaloniaApplication2.ViewModels;
using System.Linq;

namespace AvaloniaApplication2.Views
{
    public partial class ProjectView : UserControl
    {
        public ProjectView()
        {
            InitializeComponent();
        }

        public void OnProjectTapped(object? sender, TappedEventArgs e)
        {
            if (e.Source is Control source && 
                source.GetSelfAndVisualAncestors().OfType<Border>().FirstOrDefault(b => b.DataContext is ProjectItem) is { } border &&
                border.DataContext is ProjectItem projectItem &&
                DataContext is ProjectViewModel viewModel)
            {
                if (viewModel.SelectProjectCommand.CanExecute(projectItem))
                {
                    viewModel.SelectProjectCommand.Execute(projectItem);
                }
            }
        }
    }
}
