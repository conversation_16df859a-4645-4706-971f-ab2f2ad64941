using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Input;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Threading;
using System.ComponentModel;
using Avalonia.Interactivity;
using Avalonia.VisualTree;
using Avalonia.Platform.Storage;
using AvaloniaApplication2.Models;
using AvaloniaApplication2.ViewModels;
using AvaloniaApplication2.Services;
using AvaloniaApplication2.Windows;
using CommunityToolkit.Mvvm.Messaging;
using AvaloniaApplication2.Messages;
using System.Linq;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using Avalonia.Input;

namespace AvaloniaApplication2.Views
{
    public partial class GalleryView : UserControl
    {
        private readonly IImageEditService _imageEditService = new ImageEditService();
        // Live-adjust sessions
        private List<(GalleryItem item, string original)>? _brightnessSession;
        private List<(GalleryItem item, string original)>? _contrastSession;
        private CancellationTokenSource? _brightnessCts;
        private CancellationTokenSource? _contrastCts;
        public GalleryView()
        {
            InitializeComponent();
            this.DataContextChanged += OnDataContextChanged;
        }

        private ItemsControl? _itemsControl;
        private ScrollViewer? _scrollViewer;
        private INotifyPropertyChanged? _vmNotify;

        private void EnsureControls()
        {
            _itemsControl ??= this.FindControl<ItemsControl>("GalleryItemsControl");
            _scrollViewer ??= this.FindControl<ScrollViewer>("GalleryScroll");
        }

        private void OnDataContextChanged(object? sender, EventArgs e)
        {
            if (_vmNotify != null)
            {
                _vmNotify.PropertyChanged -= OnVmPropertyChanged;
                _vmNotify = null;
            }
            if (DataContext is GalleryViewModel vm)
            {
                _vmNotify = vm;
                _vmNotify.PropertyChanged += OnVmPropertyChanged;
            }
        }

        private void OnVmPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(GalleryViewModel.CurrentItem))
            {
                BringCurrentIntoView();
            }
        }

        private void BringCurrentIntoView()
        {
            EnsureControls();
            if (_itemsControl == null) return;
            if (DataContext is not GalleryViewModel vm || vm.CurrentItem == null) return;
            var items = vm.GalleryItems;
            if (items == null || items.Count == 0) return;
            var idx = items.IndexOf(vm.CurrentItem);
            if (idx < 0) return;

            // Defer until layout ready
            Dispatcher.UIThread.Post(() =>
            {
                var container = _itemsControl.ContainerFromIndex(idx) as Control;
                container?.BringIntoView();
            }, DispatcherPriority.Background);
        }

        private async void OnImportSingleClick(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            var top = TopLevel.GetTopLevel(this);
            if (top == null) return;

            var files = await top.StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
            {
                Title = "选择图像",
                AllowMultiple = true,
                FileTypeFilter = new List<FilePickerFileType>
                {
                    new FilePickerFileType("Images")
                    {
                        Patterns = new List<string>{"*.png","*.jpg","*.jpeg","*.bmp","*.gif","*.tif","*.tiff","*.webp"}
                    }
                }
            });

            if (files == null || files.Count == 0) return;

            var vm = DataContext as GalleryViewModel;
            if (vm == null) return;

            var imagePaths = files
                .Select(f => f.TryGetLocalPath())
                .Where(p => !string.IsNullOrWhiteSpace(p))
                .Cast<string>()
                .ToList();

            vm.AddImages(imagePaths);
        }

        private async void OnImportFolderClick(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            var top = TopLevel.GetTopLevel(this);
            if (top == null) return;

            var folders = await top.StorageProvider.OpenFolderPickerAsync(new FolderPickerOpenOptions
            {
                Title = "选择包含图像的文件夹",
                AllowMultiple = false
            });

            if (folders == null || folders.Count == 0) return;

            var folder = folders[0];
            var localPath = folder.TryGetLocalPath();
            if (string.IsNullOrWhiteSpace(localPath)) return;

            var vm = DataContext as GalleryViewModel;
            if (vm == null) return;

            var allowed = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                ".png",".jpg",".jpeg",".bmp",".gif",".tif",".tiff",".webp"
            };

            IEnumerable<string> EnumerateImages(string dir)
            {
                try
                {
                    return Directory.EnumerateFiles(dir)
                        .Where(p => allowed.Contains(Path.GetExtension(p)));
                }
                catch
                {
                    return Enumerable.Empty<string>();
                }
            }

            var imagePaths = EnumerateImages(localPath)
                .OrderBy(p => Path.GetFileName(p), StringComparer.OrdinalIgnoreCase)
                .ToList();

            // 清空现有显示，再导入文件夹内图像
            vm.ClearImages();
            vm.AddImages(imagePaths);
        }

        private void OnItemPointerPressed(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is Border border && border.DataContext is GalleryItem item)
            {
                if (DataContext is GalleryViewModel vm)
                {
                    var pe = e as PointerPressedEventArgs;
                    var modifiers = pe?.KeyModifiers ?? KeyModifiers.None;
                    var ctrl = modifiers.HasFlag(KeyModifiers.Control) || modifiers.HasFlag(KeyModifiers.Meta);

                    if (ctrl)
                    {
                        // Ctrl 点击：切换选中状态，实现批量多选
                        item.IsSelected = !item.IsSelected;
                        vm.SetCurrent(item);
                    }
                    else
                    {
                        // 普通点击：单选
                        foreach (var it in vm.GalleryItems)
                            it.IsSelected = false;
                        item.IsSelected = true;
                        vm.SetCurrent(item);
                    }
                    // selection changed, refresh sessions if popup is open
                    RefreshSessionsIfPopupOpen();
                }
            }
        }

        private void OnToggleItemSelectClick(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is Button btn && btn.DataContext is GalleryItem item)
            {
                item.IsSelected = !item.IsSelected;
                e.Handled = true;
                if (DataContext is GalleryViewModel vm)
                {
                    vm.SetCurrent(item);
                }
                RefreshSessionsIfPopupOpen();
            }
        }

        private void OnAdjustBrightnessClick(object? sender, RoutedEventArgs e) { }

        private void OnAdjustContrastClick(object? sender, RoutedEventArgs e) { }

        private async void OnItemDoubleTapped(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is Border border && border.DataContext is GalleryItem item)
            {
                // 发送消息给主窗体VM，切换到 ImageView 并显示该图像
                if (!string.IsNullOrWhiteSpace(item.ImagePath))
                {
                    WeakReferenceMessenger.Default.Send(new OpenImageMessage(item.ImagePath));
                }
                e.Handled = true;
            }
        }

        private void OnRemoveSelectedClick(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (DataContext is GalleryViewModel vm)
            {
                vm.RemoveSelected();
            }
        }

        private void OnCycleThumbSizeClick(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (DataContext is GalleryViewModel vm)
            {
                vm.CycleThumbnailSize();
            }
        }

        // ========== Flyout + Slider handlers ==========
        private void OnBrightnessFlyoutOpened(object? sender, EventArgs e)
        {
            if (DataContext is not GalleryViewModel vm) return;
            var selected = vm.GalleryItems.Where(i => i.IsSelected).ToList();
            if (selected.Count == 0)
            {
                _brightnessSession = null;
                return;
            }
            _brightnessSession = selected.Select(i => (i, i.ImagePath)).ToList();
        }

        private void OnBrightnessFlyoutClosed(object? sender, EventArgs e)
        {
            _brightnessCts?.Cancel();
            _brightnessCts = null;
            _brightnessSession = null;
        }

        private async void OnBrightnessSliderPropertyChanged(object? sender, Avalonia.AvaloniaPropertyChangedEventArgs e)
        {
            if (e.Property.Name != nameof(Slider.Value)) return;
            if (_brightnessSession == null || _brightnessSession.Count == 0) return;
            if (sender is not Slider slider) return;

            var factor = (float)slider.Value; // 0.5 - 1.5
            _brightnessCts?.Cancel();
            _brightnessCts = new CancellationTokenSource();
            var token = _brightnessCts.Token;

            try
            {
                await Task.Delay(120, token); // debounce
                var session = _brightnessSession?.ToList();
                if (session == null || session.Count == 0) return;

                var tasks = session.Select(async pair =>
                {
                    if (token.IsCancellationRequested) return;
                    var newPath = await Task.Run(() => _imageEditService.AdjustBrightness(pair.original, factor), token);
                    if (token.IsCancellationRequested) return;
                    pair.item.ImagePath = newPath;
                });
                await Task.WhenAll(tasks);
            }
            catch (TaskCanceledException) { }
        }

        // ===== Popup versions (since Flyout not available) =====
        private void OnSizeButtonClick(object? sender, RoutedEventArgs e)
        {
            CloseAllPopupsExcept("PopupSize");
            var popup = this.FindControl<Popup>("PopupSize");
            if (popup != null) popup.IsOpen = !popup.IsOpen;
        }

        private void OnBrightnessButtonClick(object? sender, RoutedEventArgs e)
        {
            CloseAllPopupsExcept("PopupBrightness");
            var popup = this.FindControl<Popup>("PopupBrightness");
            if (popup != null) popup.IsOpen = !popup.IsOpen;
        }

        private void OnContrastButtonClick(object? sender, RoutedEventArgs e)
        {
            CloseAllPopupsExcept("PopupContrast");
            var popup = this.FindControl<Popup>("PopupContrast");
            if (popup != null) popup.IsOpen = !popup.IsOpen;
        }

        private void OnSizePopupOpened(object? sender, EventArgs e)
        {
            // Size slider is two-way bound to ThumbnailSize; nothing else required.
        }

        private void OnSizeSliderPropertyChanged(object? sender, Avalonia.AvaloniaPropertyChangedEventArgs e)
        {
            // Value is two-way bound to ThumbnailSize; no extra action needed.
        }

        private void OnBrightnessPopupOpened(object? sender, EventArgs e)
        {
            if (DataContext is not GalleryViewModel vm) return;
            var selected = vm.GalleryItems.Where(i => i.IsSelected).ToList();
            _brightnessSession = selected.Count == 0 ? null : selected.Select(i => (i, i.ImagePath)).ToList();
            // ensure others are closed
            CloseAllPopupsExcept("PopupBrightness");
        }

        private void OnBrightnessPopupClosed(object? sender, EventArgs e)
        {
            _brightnessCts?.Cancel();
            _brightnessCts = null;
            _brightnessSession = null;
        }

        private void OnContrastPopupOpened(object? sender, EventArgs e)
        {
            if (DataContext is not GalleryViewModel vm) return;
            var selected = vm.GalleryItems.Where(i => i.IsSelected).ToList();
            _contrastSession = selected.Count == 0 ? null : selected.Select(i => (i, i.ImagePath)).ToList();
            CloseAllPopupsExcept("PopupContrast");
        }

        private void OnContrastPopupClosed(object? sender, EventArgs e)
        {
            _contrastCts?.Cancel();
            _contrastCts = null;
            _contrastSession = null;
        }
        private void OnContrastFlyoutOpened(object? sender, EventArgs e)
        {
            if (DataContext is not GalleryViewModel vm) return;
            var selected = vm.GalleryItems.Where(i => i.IsSelected).ToList();
            if (selected.Count == 0)
            {
                _contrastSession = null;
                return;
            }
            _contrastSession = selected.Select(i => (i, i.ImagePath)).ToList();
        }

        private void OnContrastFlyoutClosed(object? sender, EventArgs e)
        {
            _contrastCts?.Cancel();
            _contrastCts = null;
            _contrastSession = null;
        }

        private async void OnContrastSliderPropertyChanged(object? sender, Avalonia.AvaloniaPropertyChangedEventArgs e)
        {
            if (e.Property.Name != nameof(Slider.Value)) return;
            if (_contrastSession == null || _contrastSession.Count == 0) return;
            if (sender is not Slider slider) return;

            var factor = (float)slider.Value; // 0.5 - 1.5
            _contrastCts?.Cancel();
            _contrastCts = new CancellationTokenSource();
            var token = _contrastCts.Token;

            try
            {
                await Task.Delay(120, token); // debounce
                var session = _contrastSession?.ToList();
                if (session == null || session.Count == 0) return;

                var tasks = session.Select(async pair =>
                {
                    if (token.IsCancellationRequested) return;
                    var newPath = await Task.Run(() => _imageEditService.AdjustContrast(pair.original, factor), token);
                    if (token.IsCancellationRequested) return;
                    pair.item.ImagePath = newPath;
                });
                await Task.WhenAll(tasks);
            }
            catch (TaskCanceledException) { }
        }

        // ===== Resets =====
        private void OnSizeResetClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is GalleryViewModel vm)
            {
                vm.ThumbnailSize = 128; // default
            }
        }

        private void OnBrightnessResetClick(object? sender, RoutedEventArgs e)
        {
            _brightnessCts?.Cancel();
            var session = _brightnessSession?.ToList();
            if (session != null)
            {
                foreach (var (item, original) in session)
                    item.ImagePath = original;
            }
            var slider = this.FindControl<Slider>("SliderBrightness");
            if (slider != null) slider.Value = 1.0;
        }

        private void OnContrastResetClick(object? sender, RoutedEventArgs e)
        {
            _contrastCts?.Cancel();
            var session = _contrastSession?.ToList();
            if (session != null)
            {
                foreach (var (item, original) in session)
                    item.ImagePath = original;
            }
            var slider = this.FindControl<Slider>("SliderContrast");
            if (slider != null) slider.Value = 1.0;
        }

        // ===== Helpers =====
        private void CloseAllPopupsExcept(string? keepName)
        {
            var p1 = this.FindControl<Popup>("PopupSize");
            var p2 = this.FindControl<Popup>("PopupBrightness");
            var p3 = this.FindControl<Popup>("PopupContrast");
            if (p1 != null && p1.Name != keepName) p1.IsOpen = false;
            if (p2 != null && p2.Name != keepName) p2.IsOpen = false;
            if (p3 != null && p3.Name != keepName) p3.IsOpen = false;
        }

        private void RefreshSessionsIfPopupOpen()
        {
            var brightnessOpen = this.FindControl<Popup>("PopupBrightness")?.IsOpen == true;
            var contrastOpen = this.FindControl<Popup>("PopupContrast")?.IsOpen == true;
            if (DataContext is not GalleryViewModel vm) return;
            if (brightnessOpen)
            {
                var selected = vm.GalleryItems.Where(i => i.IsSelected).ToList();
                _brightnessSession = selected.Count == 0 ? null : selected.Select(i => (i, i.ImagePath)).ToList();
            }
            if (contrastOpen)
            {
                var selected = vm.GalleryItems.Where(i => i.IsSelected).ToList();
                _contrastSession = selected.Count == 0 ? null : selected.Select(i => (i, i.ImagePath)).ToList();
            }
        }

        // ===== Tag popup handlers =====
        private void OnTagAddButtonClick(object? sender, RoutedEventArgs e)
        {
            CloseAllPopupsExcept(null);
            var popup = this.FindControl<Popup>("PopupAddTag");
            if (popup != null)
            {
                popup.Placement = PlacementMode.Right;
                popup.IsOpen = !popup.IsOpen;
            }
        }

        private void OnTagAddCancelClick(object? sender, RoutedEventArgs e)
        {
            var popup = this.FindControl<Popup>("PopupAddTag");
            if (popup != null) popup.IsOpen = false;
        }

        private void OnTagCreateClick(object? sender, RoutedEventArgs e)
        {
            // AddTagCommand 已执行（按钮绑定 Command），这里只负责收起弹窗
            var popup = this.FindControl<Popup>("PopupAddTag");
            if (popup != null) popup.IsOpen = false;
        }

        private void OnNewTagNameKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.Key != Key.Enter) return;
            if (DataContext is GalleryViewModel vm && vm.AddTagCommand?.CanExecute(null) == true)
            {
                vm.AddTagCommand.Execute(null);
                var popup = this.FindControl<Popup>("PopupAddTag");
                if (popup != null) popup.IsOpen = false;
                e.Handled = true;
            }
        }

    }
}
