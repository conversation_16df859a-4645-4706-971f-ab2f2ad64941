import cv2
import numpy as np
import math
from typing import List, Tuple


class Defect:
    def __init__(self):
        self.center = (0, 0)  # 原图坐标系
        self.radius = 0
        self.is_bright = False
        self.contrast = 0.0

    def __repr__(self):
        return f"{'亮斑' if self.is_bright else '暗斑'}: 中心{self.center}, 半径{self.radius:.2f}, 对比度{self.contrast:.2f}"


class LightGuideDetector:
    def __init__(self):
        self.threshold_factor = 3.0
        self.min_defect_area = 30
        self.min_circularity = 0.5
        self.light_guide_min_area = 1000
        self.segment_length = 500
        self.min_segment_size = 10
        self.debug = False

    def generate_contour_mask(self, contour: np.ndarray, image_size: Tuple[int, int]) -> np.ndarray:
        mask = np.zeros(image_size, dtype=np.uint8)  # (h, w)
        if len(contour.shape) == 3 and contour.shape[1] == 1:
            contour = contour.squeeze(1)
        cv2.drawContours(mask, [contour.astype(np.int32)], 0, 255, -1)
        return mask

    def detect_light_guide_contours(self, image: np.ndarray) -> List[np.ndarray]:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if image.ndim == 3 else image.copy()
        gray = cv2.GaussianBlur(gray, (5, 5), 0)
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        # cv2.drawContours(image,contours,-1,(0,0,255),2)
        # cv2.imwrite("guide.png", image)
        valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > self.light_guide_min_area]
        valid_contours.sort(key=lambda c: cv2.contourArea(c), reverse=True)
        if self.debug:
            cv2.imwrite("binary_image.jpg", binary)
        return valid_contours

    def get_light_guide_oriented_bounds(self, contour: np.ndarray) -> Tuple[np.ndarray, float, Tuple[float, float], np.ndarray, np.ndarray]:
        """
        返回：
            rotated_points: 轮廓点在旋转后图像坐标系中的坐标 (N,2) float32
            major_axis: 主轴长度（用于分段）
            center: 原图中心 (cx,cy)
            M_rot: 用来把原图->旋转图的 2x3 仿射矩阵
            M_rot_inv: 旋转矩阵的逆，用来把旋转图->原图
        重要：这里通过 boxPoints 计算真实的边角方向，避免 minAreaRect 的角度歧义。
        """
        rect = cv2.minAreaRect(contour)
        box = cv2.boxPoints(rect)  # 4x2
        box = box.reshape(4, 2)
        # 取最长边向量来定义主方向
        edges = []
        for i in range(4):
            p1 = box[i]
            p2 = box[(i + 1) % 4]
            edges.append((p2 - p1))
        edge_lengths = [np.linalg.norm(e) for e in edges]
        max_idx = int(np.argmax(edge_lengths))
        vx, vy = edges[max_idx]
        angle_rad = np.arctan2(vy, vx)
        angle_deg = np.degrees(angle_rad)
        # 目标：把主轴旋转到水平（沿 x 轴），所以旋转角度为 -angle_deg
        rot_angle = -angle_deg

        (cx, cy), (w, h), _ = rect
        major_axis = max(w, h)

        M_rot = cv2.getRotationMatrix2D((cx, cy), rot_angle, 1.0)
        try:
            M_rot_inv = cv2.invertAffineTransform(M_rot)
        except Exception as e:
            print("逆仿射计算失败，使用单位变换:", e)
            M_rot_inv = np.array([[1, 0, 0], [0, 1, 0]], dtype=np.float32)

        contour_points = contour.reshape(-1, 2).astype(np.float32)
        rotated_points = cv2.transform(contour_points[None, :, :], M_rot).squeeze(0)

        if self.debug:
            test_point = np.array([[cx, cy]], dtype=np.float32)
            rotated_center = cv2.transform(test_point, M_rot).squeeze()
            restored_center = cv2.transform(rotated_center[None, :], M_rot_inv).squeeze()
            print(f"中心验证: 原({cx:.2f},{cy:.2f}) -> 旋转({rotated_center[0]:.2f},{rotated_center[1]:.2f}) -> 恢复({restored_center[0]:.2f},{restored_center[1]:.2f})")

        return rotated_points, major_axis, (cx, cy), M_rot, M_rot_inv

    def split_light_guide_into_segments(self, image: np.ndarray, contour: np.ndarray, full_mask: np.ndarray) -> List[Tuple[np.ndarray, np.ndarray, int, int, int, int, np.ndarray]]:
        """
        返回每段：
           (seg_image, seg_mask, seg_min_x_rot, seg_min_y_rot, seg_w_rot, seg_h_rot, M_rot_inv)
        注意：这里返回的是旋转图中分段的框位置（用于精确映射每个点）。
        """
        rotated_points, major_axis, (cx, cy), M_rot, M_rot_inv = self.get_light_guide_oriented_bounds(contour)
        img_h, img_w = image.shape[:2]
        segments = []

        num_segments = max(1, int(np.ceil(major_axis / self.segment_length)))
        actual_segment_length = major_axis / num_segments

        rotated_image = cv2.warpAffine(image, M_rot, (img_w, img_h), flags=cv2.INTER_LINEAR,
                                       borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
        rotated_mask = cv2.warpAffine(full_mask, M_rot, (img_w, img_h), flags=cv2.INTER_NEAREST,
                                      borderMode=cv2.BORDER_CONSTANT, borderValue=0)

        if self.debug:
            cv2.imwrite("rotated_image.jpg", rotated_image)
            cv2.imwrite("rotated_mask.jpg", rotated_mask)

        min_x_rot = float(np.min(rotated_points[:, 0]))
        max_x_rot = float(np.max(rotated_points[:, 0]))
        center_x_rot = (min_x_rot + max_x_rot) / 2.0

        for i in range(num_segments):
            seg_start_x_rot = center_x_rot - (major_axis / 2.0) + i * actual_segment_length
            seg_end_x_rot = seg_start_x_rot + actual_segment_length

            point_mask = (rotated_points[:, 0] >= seg_start_x_rot) & (rotated_points[:, 0] <= seg_end_x_rot)
            segment_points_rot = rotated_points[point_mask]
            if len(segment_points_rot) < 5:
                continue

            seg_min_x_rot = int(np.floor(np.min(segment_points_rot[:, 0])) - 2)
            seg_max_x_rot = int(np.ceil(np.max(segment_points_rot[:, 0])) + 2)
            seg_min_y_rot = int(np.floor(np.min(segment_points_rot[:, 1])) - 2)
            seg_max_y_rot = int(np.ceil(np.max(segment_points_rot[:, 1])) + 2)

            seg_min_x_rot = max(0, seg_min_x_rot)
            seg_max_x_rot = min(rotated_image.shape[1] - 1, seg_max_x_rot)
            seg_min_y_rot = max(0, seg_min_y_rot)
            seg_max_y_rot = min(rotated_image.shape[0] - 1, seg_max_y_rot)

            seg_w_rot = seg_max_x_rot - seg_min_x_rot + 1
            seg_h_rot = seg_max_y_rot - seg_min_y_rot + 1
            if seg_w_rot < self.min_segment_size or seg_h_rot < self.min_segment_size:
                continue

            seg_image = rotated_image[seg_min_y_rot:seg_max_y_rot + 1, seg_min_x_rot:seg_max_x_rot + 1].copy()
            seg_mask = rotated_mask[seg_min_y_rot:seg_max_y_rot + 1, seg_min_x_rot:seg_max_x_rot + 1].copy()

            if self.debug:
                print(f"分段{i}: rot_rect=({seg_min_x_rot},{seg_min_y_rot},{seg_w_rot},{seg_h_rot})")

            segments.append((seg_image, seg_mask, seg_min_x_rot, seg_min_y_rot, seg_w_rot, seg_h_rot, M_rot_inv))

        return segments

    def detect_bright_dark_spots(self, segment_image: np.ndarray, segment_mask: np.ndarray) -> List[Defect]:
        defects = []
        if segment_image is None or segment_mask is None or np.sum(segment_mask) < 10:
            return defects

        gray = cv2.cvtColor(segment_image, cv2.COLOR_BGR2GRAY) if segment_image.ndim == 3 else segment_image.copy()
        roi_pixels = gray[segment_mask == 255]
        if roi_pixels.size == 0:
            return defects
        roi_pixels = np.sort(roi_pixels)
        if len(roi_pixels) > 100:
            low = int(0.01 * len(roi_pixels))
            high = int(0.99 * len(roi_pixels))
            roi_pixels = roi_pixels[low:high]
        mean_val, std_val = float(np.mean(roi_pixels)), float(np.std(roi_pixels))
        if std_val < 1e-6:
            return defects

        bright_thresh = mean_val + self.threshold_factor * std_val
        dark_thresh = mean_val - self.threshold_factor * std_val

        bright_mask = np.logical_and(gray > bright_thresh, segment_mask == 255).astype(np.uint8) * 255
        dark_mask = np.logical_and(gray < dark_thresh, segment_mask == 255).astype(np.uint8) * 255

        defects.extend(self._extract_circular_defects(bright_mask, gray, mean_val, is_bright=True))
        defects.extend(self._extract_circular_defects(dark_mask, gray, mean_val, is_bright=False))
        return defects

    def _extract_circular_defects(self, spot_mask: np.ndarray, gray: np.ndarray, mean_val: float, is_bright: bool) -> List[Defect]:
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        spot_mask = cv2.morphologyEx(spot_mask, cv2.MORPH_CLOSE, kernel)
        contours, _ = cv2.findContours(spot_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        defects = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < self.min_defect_area:
                continue
            perimeter = cv2.arcLength(cnt, True)
            if perimeter == 0:
                continue
            circularity = 4 * np.pi * area / (perimeter ** 2)
            if circularity < self.min_circularity:
                continue
            (x, y), radius = cv2.minEnclosingCircle(cnt)
            center = (int(round(x)), int(round(y)))
            radius = max(1, int(round(radius)))
            # 计算局部对比度（使用圆内区域）
            y1, y2 = max(0, center[1] - radius), min(gray.shape[0], center[1] + radius)
            x1, x2 = max(0, center[0] - radius), min(gray.shape[1], center[0] + radius)
            roi = gray[y1:y2, x1:x2]
            if roi.size == 0:
                continue
            spot_mean = float(np.mean(roi))
            contrast = spot_mean - mean_val if is_bright else mean_val - spot_mean
            d = Defect()
            d.center, d.radius = center, radius
            d.is_bright, d.contrast = is_bright, contrast
            defects.append(d)
        return defects

    def _circle_iou(self, x1, y1, r1, x2, y2, r2):
        # 计算两个圆的IOU
        d = math.hypot(x1 - x2, y1 - y2)
        if d >= r1 + r2:
            return 0.0
        if d <= abs(r1 - r2):
            return min(r1, r2) ** 2 / max(r1, r2) ** 2
        r1s, r2s = r1 * r1, r2 * r2
        alpha = math.acos(max(-1.0, min(1.0, (d * d + r1s - r2s) / (2 * d * r1))))
        beta = math.acos(max(-1.0, min(1.0, (d * d + r2s - r1s) / (2 * d * r2))))
        inter = r1s * alpha + r2s * beta - 0.5 * math.sqrt(
            max(0.0, (r1 + r2 - d) * (d + r1 - r2) * (d - r1 + r2) * (d + r1 + r2)))
        area1 = math.pi * r1s
        area2 = math.pi * r2s
        return inter / (area1 + area2 - inter)

    def merge_duplicate_defects(self, defects: List[Defect], iou_thresh: float = 0.25, dist_ratio: float = 0.6) -> List[Defect]:
        """
        合并同一光导内重复检测到的缺陷。
        参数:
            defects: 已经映射回原图坐标的缺陷列表（同一光导）
            iou_thresh: 当 IoU > 阈值 时认为是同一缺陷（0..1）
            dist_ratio: 当中心距 < dist_ratio * max(r1,r2) 时也认为是同一缺陷
        返回:
            合并后的缺陷列表
        """
        n = len(defects)
        if n <= 1:
            return defects.copy()

        parent = list(range(n))
        def find(a):
            while parent[a] != a:
                parent[a] = parent[parent[a]]
                a = parent[a]
            return a
        def union(a, b):
            ra, rb = find(a), find(b)
            if ra != rb:
                parent[rb] = ra

        for i in range(n):
            for j in range(i + 1, n):
                di = defects[i]; dj = defects[j]
                if di.is_bright != dj.is_bright:
                    continue
                d = math.hypot(di.center[0] - dj.center[0], di.center[1] - dj.center[1])
                maxr = max(di.radius, dj.radius, 1.0)
                if d < dist_ratio * maxr:
                    union(i, j)
                    continue
                iou = self._circle_iou(di.center[0], di.center[1], di.radius, dj.center[0], dj.center[1], dj.radius)
                if iou > iou_thresh:
                    union(i, j)

        clusters = {}
        for idx in range(n):
            root = find(idx)
            clusters.setdefault(root, []).append(idx)

        merged = []
        for root, idxs in clusters.items():
            if len(idxs) == 1:
                merged.append(defects[idxs[0]])
                continue
            total_area = 0.0
            weighted_x = 0.0
            weighted_y = 0.0
            weighted_contrast = 0.0
            is_bright = defects[idxs[0]].is_bright
            for k in idxs:
                d = defects[k]
                area = math.pi * (d.radius ** 2)
                total_area += area
                weighted_x += d.center[0] * area
                weighted_y += d.center[1] * area
                weighted_contrast += d.contrast * area
            if total_area <= 0:
                continue
            cx = int(round(weighted_x / total_area))
            cy = int(round(weighted_y / total_area))
            new_radius = int(round(math.sqrt(total_area / math.pi)))
            new_contrast = float(weighted_contrast / total_area)
            md = Defect()
            md.center = (cx, cy)
            md.radius = max(1, new_radius)
            md.is_bright = is_bright
            md.contrast = new_contrast
            merged.append(md)

        return merged

    def process_light_guide_segments(self, image: np.ndarray, contour: np.ndarray, full_mask: np.ndarray) -> List[Defect]:
        guide_defects = []
        segments = self.split_light_guide_into_segments(image, contour, full_mask)
        if not segments:
            return guide_defects

        for seg_idx, (seg_image, seg_mask, seg_min_x_rot, seg_min_y_rot, seg_w_rot, seg_h_rot, M_rot_inv) in enumerate(segments):
            if self.debug:
                cv2.imwrite(f"segment_{seg_idx}_image.jpg", seg_image)
                cv2.imwrite(f"segment_{seg_idx}_mask.jpg", seg_mask)

            seg_defects = self.detect_bright_dark_spots(seg_image, seg_mask)
            if not seg_defects:
                continue

            # 把分段内每个缺陷的“旋转图坐标”（相对于整张旋转图）映射回原图
            for defect in seg_defects:
                abs_rot_x = seg_min_x_rot + defect.center[0]
                abs_rot_y = seg_min_y_rot + defect.center[1]
                pt_rot = np.array([[[float(abs_rot_x), float(abs_rot_y)]]], dtype=np.float32)
                pt_orig = cv2.transform(pt_rot, M_rot_inv).squeeze()
                mapped_x = int(round(float(pt_orig[0])))
                mapped_y = int(round(float(pt_orig[1])))

                adjusted_defect = Defect()
                adjusted_defect.center = (mapped_x, mapped_y)
                adjusted_defect.radius = defect.radius
                adjusted_defect.is_bright = defect.is_bright
                adjusted_defect.contrast = defect.contrast
                guide_defects.append(adjusted_defect)

        # 在返回前对同一光导内的重复检测进行合并
        guide_defects = self.merge_duplicate_defects(guide_defects, iou_thresh=0.01, dist_ratio=0.1)

        return guide_defects

    def process_image(self, image: np.ndarray) -> Tuple[List[Defect], List[np.ndarray], List[np.ndarray]]:
        all_defects = []
        image_size = (image.shape[0], image.shape[1])
        light_guide_contours = self.detect_light_guide_contours(image)
        if not light_guide_contours:
            return all_defects, [], light_guide_contours
        light_guide_masks = self.get_light_guide_masks(light_guide_contours, image_size)
        for guide_idx, (cnt, full_mask) in enumerate(zip(light_guide_contours, light_guide_masks), 1):
            if self.debug:
                cv2.imwrite(f"guide_{guide_idx}_mask.jpg", full_mask)
            guide_defects = self.process_light_guide_segments(image, cnt, full_mask)
            all_defects.extend(guide_defects)
            if self.debug:
                seg_count = len(self.split_light_guide_into_segments(image, cnt, full_mask))
                print(f"光导{guide_idx}: 分段数={seg_count}, 缺陷数={len(guide_defects)}")
        return all_defects, light_guide_masks, light_guide_contours

    def get_light_guide_masks(self, contours: List[np.ndarray], image_size: Tuple[int, int]) -> List[np.ndarray]:
        return [self.generate_contour_mask(cnt, image_size) for cnt in contours]

    def draw_results(self, image: np.ndarray, defects: List[Defect], light_guide_contours: List[np.ndarray]) -> np.ndarray:
        result = image.copy()

        for i, defect in enumerate(defects):
            color = (0, 0, 255) if defect.is_bright else (255, 0, 0)
            cv2.circle(result, defect.center, defect.radius, color, 2)
            cv2.putText(result, f"{i + 1}", (defect.center[0] + 5, defect.center[1] - 5),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        return result


if __name__ == "__main__":
    image_path = r"E:\167\167_826_defect.jpg"
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        exit(1)

    detector = LightGuideDetector()
    detector.threshold_factor = 2.0
    detector.min_defect_area = 20
    detector.min_circularity = 0.6
    detector.segment_length = 150
    detector.light_guide_min_area = 5000
    # detector.debug = True

    all_defects, light_guide_masks, light_guide_contours = detector.process_image(image)
    result_image = detector.draw_results(image, all_defects, light_guide_contours)

    print("\n" + "=" * 50)
    print(f"检测到光导数量: {len(light_guide_contours)}")
    print(f"总缺陷数: {len(all_defects)}")
    if all_defects:
        print("缺陷详情:")
        for i, defect in enumerate(all_defects, 1):
            print(f"  {i}. {defect}")
    print("=" * 50)

    output_path = "167_826_defect.jpg"
    cv2.imwrite(output_path, result_image)
    print(f"结果图已保存为: {output_path}")
